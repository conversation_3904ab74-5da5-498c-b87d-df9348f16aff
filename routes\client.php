<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\Client\CartController;
use App\Http\Controllers\Api\Client\OrderController;
use App\Http\Controllers\Api\Client\OrderRateController;
use App\Http\Controllers\Api\Client\OrderReportController;
use App\Http\Controllers\Api\Client\AddressController;
use App\Http\Controllers\Api\Client\ProductController;
use App\Http\Controllers\Api\Client\CategoryController;
use App\Http\Controllers\Api\Client\FavoriteController;
use App\Http\Controllers\Api\Client\ContactUsController;
use App\Http\Controllers\Api\Client\GiftController;
use App\Http\Controllers\Api\Client\AuthController;

Route::group([
    'namespace'  => 'Api',
    'middleware' => ['api-cors', 'api-lang'],
    'prefix'     => 'client',
], function () {

    Route::group(['middleware' => ['guest']], function () {
        /***************************** AuthController Start *****************************/
        Route::post('register', [AuthController::class, 'register'])->name('client.auth.register');
        Route::post('login', [AuthController::class, 'login'])->name('client.auth.login');
        /***************************** AuthController End *****************************/

        /***************************** CategoryController start *****************************/
        Route::get('categories', [CategoryController::class, 'index'])->name('client.categories.index');
        Route::get('categories/{id}', [CategoryController::class, 'show'])->name('client.categories.show');
        /***************************** CategoryController End *****************************/
    });

    Route::group(['middleware' => ['auth:sanctum', 'is-active', 'check-client-type']], function () {
        /***************************** AuthController Start *****************************/
        Route::get('profile', [AuthController::class, 'getProfile'])->name('client.auth.profile');
        Route::post('update-profile', [AuthController::class, 'updateProfile'])->name('client.auth.update-profile');
        /***************************** AuthController End *****************************/

        /***************************** AddressController start *****************************/
        Route::apiResource('addresses', AddressController::class, [
            'names' => [
                'index' => 'client.addresses.index',
                'store' => 'client.addresses.store',
                'show' => 'client.addresses.show',
                'update' => 'client.addresses.update',
                'destroy' => 'client.addresses.destroy',
            ]
        ]);

        Route::post('favorite/toggle', [FavoriteController::class, 'toggle'])->name('client.favorite.toggle');
        /***************************** AddressController end *****************************/

        /***************************** CartController start *****************************/
        Route::get('cart', [CartController::class, 'getCart'])->name('client.cart.get');
        Route::post('cart/add', [CartController::class, 'addToCart'])->name('client.cart.add');
        Route::put('cart/update', [CartController::class, 'updateCartItem'])->name('client.cart.update');
        Route::delete('cart/remove', [CartController::class, 'removeFromCart'])->name('client.cart.remove');
        Route::delete('cart/clear', [CartController::class, 'clearCart'])->name('client.cart.clear');

        // Coupon and loyalty points
        Route::post('cart/apply-coupon', [CartController::class, 'applyCoupon'])->name('client.cart.apply-coupon');
        Route::delete('cart/remove-coupon', [CartController::class, 'removeCoupon'])->name('client.cart.remove-coupon');
        Route::post('cart/apply-loyalty-points', [CartController::class, 'applyLoyaltyPoints'])->name('client.cart.apply-loyalty-points');
        Route::delete('cart/remove-loyalty-points', [CartController::class, 'removeLoyaltyPoints'])->name('client.cart.remove-loyalty-points');
        /***************************** CartController end *****************************/

        /***************************** OrderController start *****************************/
        Route::get('orders', [OrderController::class, 'getOrders'])->name('client.orders.list');
        Route::get('orders/{id}', [OrderController::class, 'getOrder'])->name('client.orders.show');
        Route::post('orders/create', [OrderController::class, 'createOrder'])->name('client.orders.create');
        Route::post('orders/cancel', [OrderController::class, 'cancelOrder'])->name('client.orders.cancel');
        /***************************** OrderController end *****************************/

        /***************************** OrderRateController start *****************************/
        Route::post('orders/rate', [OrderRateController::class, 'store'])->name('client.orders.store');
        /***************************** OrderRateController end *****************************/

        /***************************** OrderReportController start *****************************/
        Route::post('orders/report', [OrderReportController::class, 'store'])->name('client.orders.store');
        /***************************** OrderReportController end *****************************/

        /***************************** GiftController start *****************************/
        Route::get('gifts', [GiftController::class, 'index'])->name('client.gifts.index');
        Route::get('gifts/available', [GiftController::class, 'available'])->name('client.gifts.available');
        Route::get('gifts/pending', [GiftController::class, 'pending'])->name('client.gifts.pending');
        Route::get('gifts/stats', [GiftController::class, 'stats'])->name('client.gifts.stats');
        /***************************** GiftController end *****************************/
    });
});
