<?php if(isset($inputs)): ?>
    <?php $__currentLoopData = $inputs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $inputName => $input): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        
        
        <?php if($input['input'] == 'image'): ?>
            <div class="col-12 <?php echo e(isset($input['col_md']) ? ' col-md-' . $input['col_md'] : ' '); ?>">
                <div class="imgMontg col-12 text-center ">
                    <div class="dropBox">
                        <div class="textCenter">
                            <div class="imagesUploadBlock">
                                <?php if(isset($input['text'])): ?>
                                    <h6><?php echo e($input['text']); ?></h6>
                                <?php endif; ?>
                                <label class="uploadImg">
                                    <span><i class="feather icon-image"></i></span>
                                    <input type="file" accept="image/*" name="<?php echo e($inputName); ?>" class="imageUploader"

                                    <?php if(isset($input['validation']) && $input['validation'] != false): ?>
                                        <?php $__currentLoopData = $input['validation']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $singleValidation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(isset($singleValidation['value'])): ?>
                                            <?php echo e($singleValidation['type'] . '=' . $singleValidation['value']); ?>

                                            <?php else: ?>
                                            <?php echo e($singleValidation['type']); ?>

                                            <?php endif; ?>
                                            data-validation-<?php echo e($singleValidation['type']); ?>-message='<?php echo e($singleValidation['message']); ?>'
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php elseif(!isset($input['validation'])): ?>
                                        required
                                        data-validation-required-message='<?php echo e(__('admin.this_field_is_required')); ?>' 
                                    <?php endif; ?>

                                    <?php if(isset($input['attributes'])): ?>
                                        <?php $__currentLoopData = $input['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php echo e($key); ?>="<?php echo e($value); ?>"
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                    >
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        
        <?php elseif($input['input'] == 'files'): ?>
        <div class="  col-12 <?php echo e(isset($input['col_md']) ? ' col-md-' . $input['col_md'] : ' '); ?>">
            <div class="form-group">
                <label for="first-name-column"><?php echo e($input['text']); ?></label>
                <div class="controls">
                    <input
                        type="file"
                        multiple
                        name="<?php echo e($inputName); ?>[]" 
                        id="<?php echo e($inputName); ?>_input"
                        class="form-control files-input"
                        data-input="<?php echo e($inputName); ?>"
                        placeholder="<?php echo e(isset($input['placeholder']) ? $input['placeholder'] : __('admin.enter') . ' ' . $input['text']); ?>" 
                        
                        <?php if(isset($input['validation']) && $input['validation'] != false): ?>
                            <?php $__currentLoopData = $input['validation']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $singleValidation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(isset($singleValidation['value'])): ?>
                                <?php echo e($singleValidation['type'] . '=' . $singleValidation['value']); ?>

                                <?php else: ?>
                                <?php echo e($singleValidation['type']); ?>

                                <?php endif; ?>
                                data-validation-<?php echo e($singleValidation['type']); ?>-message='<?php echo e($singleValidation['message']); ?>'
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php elseif(!isset($input['validation'])): ?>
                            required
                            data-validation-required-message='<?php echo e(__('admin.this_field_is_required')); ?>' 
                        <?php endif; ?>
                        
                        <?php if(isset($input['attributes'])): ?>
                            <?php $__currentLoopData = $input['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($key); ?>="<?php echo e($value); ?>"
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    >
                </div>
                <div class="col-12 p-0 ">
                    <div class="files_uploader_container  p-2" id="<?php echo e($inputName); ?>_cont"></div>
                </div>
            </div>
        </div>



        
        <?php elseif($input['input'] == 'input_ar_en'): ?>
            <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-6  col-12 <?php echo e(isset($input['col_md']) ? ' col-md-' . $input['col_md'] : ' '); ?>">
                    <div class="form-group">
                        <label for="first-name-column"><?php echo e($input['text'][$lang] ? $input['text'][$lang] : $input['text']); ?></label>
                        <div class="controls">
                            <input 
                                type="<?php echo e(isset($input['type']) ? $input['type'] : 'text'); ?>" 
                                name="<?php echo e($inputName); ?>[<?php echo e($lang); ?>]" 
                                class="form-control" placeholder="<?php echo e(isset($input['placeholder']) ? $input['placeholder'][$lang] : __('admin.enter') . ' ' . $input['text'][$lang]); ?>" 
                                
    
                                <?php if(isset($input['validation']) && $input['validation'] != false): ?>
                                    <?php $__currentLoopData = $input['validation']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $singleValidation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if(isset($singleValidation['value'])): ?>
                                        <?php echo e($singleValidation['type'] . '=' . $singleValidation['value']); ?>

                                        <?php else: ?>
                                        <?php echo e($singleValidation['type']); ?>

                                        <?php endif; ?>
                                        data-validation-<?php echo e($singleValidation['type']); ?>-message='<?php echo e($singleValidation['message']); ?>'
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php elseif(!isset($input['validation'])): ?>
                                    required
                                    data-validation-required-message='<?php echo e(__('admin.this_field_is_required')); ?>' 
                                <?php endif; ?>

                                <?php if(isset($input['attributes'])): ?>
                                    <?php $__currentLoopData = $input['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php echo e($key); ?>="<?php echo e($value); ?>"
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            >
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



        
        <?php elseif($input['input'] == 'input'): ?>
            <div class="col-md-6  col-12 <?php echo e(isset($input['col_md']) ? ' col-md-' . $input['col_md'] : ' '); ?>">
                <div class="form-group">
                    <label for="first-name-column"><?php echo e($input['text']); ?></label>
                    <div class="controls">
                        <input
                            type="<?php echo e(isset($input['type']) ? $input['type'] : 'text'); ?>" 
                            name="<?php echo e($inputName); ?>" 
                            class="form-control" 
                            placeholder="<?php echo e(isset($input['placeholder']) ? $input['placeholder'] : __('admin.enter') . ' ' . $input['text']); ?>" 
                            
                            <?php if(isset($input['validation']) && $input['validation'] != false): ?>
                                <?php $__currentLoopData = $input['validation']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $singleValidation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(isset($singleValidation['value'])): ?>
                                    <?php echo e($singleValidation['type'] . '=' . $singleValidation['value']); ?>

                                    <?php else: ?>
                                    <?php echo e($singleValidation['type']); ?>

                                    <?php endif; ?>
                                    data-validation-<?php echo e($singleValidation['type']); ?>-message='<?php echo e($singleValidation['message']); ?>'
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php elseif(!isset($input['validation'])): ?>
                                required
                                data-validation-required-message='<?php echo e(__('admin.this_field_is_required')); ?>' 
                            <?php endif; ?>
                            
                            <?php if(isset($input['attributes'])): ?>
                                <?php $__currentLoopData = $input['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php echo e($key); ?>="<?php echo e($value); ?>"
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        >
                    </div>
                </div>
            </div>




        
        <?php elseif($input['input'] == 'textarea_ar_en'): ?>
            <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-6  col-12 <?php echo e(isset($input['col_md']) ? ' col-md-' . $input['col_md'] : ' '); ?>">
                    <div class="form-group">
                        <label for="first-name-column"><?php echo e($input['text'][$lang] ? $input['text'][$lang] : $input['text']); ?></label>
                        <div class="controls">
                            <textarea 
                                name="<?php echo e($inputName); ?>[<?php echo e($lang); ?>]" 
                                class="form-control" placeholder="<?php echo e(isset($input['placeholder']) ? $input['placeholder'][$lang] : __('admin.enter') . ' ' . $input['text'][$lang]); ?>" 
                                
                                <?php if(isset($input['validation']) && $input['validation'] != false): ?>
                                    <?php $__currentLoopData = $input['validation']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $singleValidation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if(isset($singleValidation['value'])): ?>
                                        <?php echo e($singleValidation['type'] . '=' . $singleValidation['value']); ?>

                                        <?php else: ?>
                                        <?php echo e($singleValidation['type']); ?>

                                        <?php endif; ?>
                                        data-validation-<?php echo e($singleValidation['type']); ?>-message='<?php echo e($singleValidation['message']); ?>'
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php elseif(!isset($input['validation'])): ?>
                                    required
                                    data-validation-required-message='<?php echo e(__('admin.this_field_is_required')); ?>' 
                                <?php endif; ?>

                                <?php echo e(!isset($input['attributes']['rows']) ? 'rows=6' : ''); ?>

                                <?php if(isset($input['attributes'])): ?>
                                    <?php $__currentLoopData = $input['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php echo e($key); ?>="<?php echo e($value); ?>"
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            ></textarea>
                            <?php if(isset($input['ckeditor']) && $input['ckeditor'] === true): ?>
                                <div class="error <?php echo e($inputName); ?> <?php echo e($lang); ?>"></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



        
        <?php elseif($input['input'] == 'textarea'): ?>
            <div class="col-md-6  col-12 <?php echo e(isset($input['col_md']) ? ' col-md-' . $input['col_md'] : ' '); ?>">
                <div class="form-group">
                    <label for="first-name-column"><?php echo e($input['text']); ?></label>
                    <div class="controls">
                        <textarea
                            name="<?php echo e($inputName); ?>" 
                            class="form-control" 
                            placeholder="<?php echo e(isset($input['placeholder']) ? $input['placeholder'] : __('admin.enter') . ' ' . $input['text']); ?>" 
                            
                            <?php if(isset($input['validation']) && $input['validation'] != false): ?>
                                <?php $__currentLoopData = $input['validation']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $singleValidation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(isset($singleValidation['value'])): ?>
                                    <?php echo e($singleValidation['type'] . '=' . $singleValidation['value']); ?>

                                    <?php else: ?>
                                    <?php echo e($singleValidation['type']); ?>

                                    <?php endif; ?>
                                    data-validation-<?php echo e($singleValidation['type']); ?>-message='<?php echo e($singleValidation['message']); ?>'
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php elseif(!isset($input['validation'])): ?>
                                required
                                data-validation-required-message='<?php echo e(__('admin.this_field_is_required')); ?>' 
                            <?php endif; ?> 
                            
                            <?php echo e(!isset($input['attributes']['rows']) ? 'rows=6' : ''); ?>

                            <?php if(isset($input['attributes'])): ?>
                                <?php $__currentLoopData = $input['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php echo e($key); ?>="<?php echo e($value); ?>"
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        ></textarea>
                        <?php if(isset($input['ckeditor']) && $input['ckeditor'] === true): ?>
                            <div class="error <?php echo e($inputName); ?>"></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>




        
        <?php elseif($input['input'] == 'single_select'): ?>
            <div class=" col-12 <?php echo e(isset($input['col_md']) ? ' col-md-' . $input['col_md'] : ' '); ?>">
                <div class="form-group">
                    <label for="first-name-column"><?php echo e($input['text']); ?></label>
                    <div class="controls">
                        <select 
                            name="<?php echo e($inputName); ?>" 
                            class="select2 form-control" 
                            
                            <?php if(isset($input['validation']) && $input['validation'] != false): ?>
                                <?php $__currentLoopData = $input['validation']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $singleValidation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(isset($singleValidation['value'])): ?>
                                    <?php echo e($singleValidation['type'] . '=' . $singleValidation['value']); ?>

                                    <?php else: ?>
                                    <?php echo e($singleValidation['type']); ?>

                                    <?php endif; ?>
                                    data-validation-<?php echo e($singleValidation['type']); ?>-message='<?php echo e($singleValidation['message']); ?>'
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php elseif(!isset($input['validation'])): ?>
                                required
                                data-validation-required-message='<?php echo e(__('admin.this_field_is_required')); ?>' 
                            <?php endif; ?>

                            <?php if(isset($input['attributes'])): ?>
                                <?php $__currentLoopData = $input['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php echo e($key); ?>="<?php echo e($value); ?>"
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        >
                            <?php if(isset($input['placeholder'])): ?>
                                <option selected disabled value><?php echo e($input['placeholder']); ?></option>
                            <?php else: ?>
                                <option selected disabled value><?php echo e(__('admin.choose') . ' ' . $input['text']); ?></option>
                            <?php endif; ?>
                            <?php $__currentLoopData = $input['options']['array']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option <?php if($input['optionData'] != null): ?> data-<?php echo e($input['optionData']['name']); ?>=<?php echo e($option[$input['optionData']['value']]); ?> <?php endif; ?> value="<?php echo e($option[$input['options']['value']]); ?>"><?php echo e($option[$input['options']['text']]); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </div>
            </div>


        <?php elseif($input['input'] == 'multiple_select'): ?>
            <div class="col-12 <?php echo e(isset($input['col_md']) ? ' col-md-' . $input['col_md'] : ' '); ?>">
                <div class="form-group">
                    <label for="first-name-column"><?php echo e($input['text']); ?></label>
                    <div class="controls">
                        <select 
                            multiple
                            name="<?php echo e($inputName); ?>[]" 
                            class="select2 <?php echo e($inputName); ?>-multiple form-control" 
                            
                            <?php if(isset($input['validation']) && $input['validation'] != false): ?>
                                <?php $__currentLoopData = $input['validation']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $singleValidation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(isset($singleValidation['value'])): ?>
                                    <?php echo e($singleValidation['type'] . '=' . $singleValidation['value']); ?>

                                    <?php else: ?>
                                    <?php echo e($singleValidation['type']); ?>

                                    <?php endif; ?>
                                    data-validation-<?php echo e($singleValidation['type']); ?>-message='<?php echo e($singleValidation['message']); ?>'
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php elseif(!isset($input['validation'])): ?>
                                required
                                data-validation-required-message='<?php echo e(__('admin.this_field_is_required')); ?>' 
                            <?php endif; ?>

                            <?php if(isset($input['attributes'])): ?>
                                <?php $__currentLoopData = $input['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php echo e($key); ?>="<?php echo e($value); ?>"
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        >
                            <?php $__currentLoopData = $input['options']['array']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($option[$input['options']['value']]); ?>"><?php echo e($option[$input['options']['text']]); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </div>
            </div>


        <?php elseif($input['input'] == 'seo'): ?>
            <h5 class="w-100 pt-5 px-3 pb-2"><?php echo e(__('admin.add_seo')); ?></h5>
            <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-6 col-12">
                    <div class="form-group">
                        <label for="first-name-column"><?php echo e(__('site.meta_title_'.$lang)); ?></label>
                        <div class="controls">
                            <textarea name="meta_title[<?php echo e($lang); ?>]" class="form-control" placeholder="<?php echo e(__('site.write') . __('site.meta_title_'.$lang)); ?>"  cols="30" rows="10"></textarea>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-6 col-12">
                    <div class="form-group">
                        <label for="first-name-column"><?php echo e(__('site.meta_description_'.$lang)); ?></label>
                        <div class="controls">
                            <textarea name="meta_description[<?php echo e($lang); ?>]" class="form-control" placeholder="<?php echo e(__('site.write') . __('site.meta_description_'.$lang)); ?>"  cols="30" rows="10"></textarea>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-6 col-12">
                    <div class="form-group">
                        <label for="first-name-column"><?php echo e(__('site.meta_keywords_'.$lang)); ?></label>
                        <div class="controls">
                            <textarea name="meta_keywords[<?php echo e($lang); ?>]" class="form-control" placeholder="<?php echo e(__('site.write') . __('site.meta_keywords_'.$lang)); ?>"  cols="30" rows="10"></textarea>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <?php elseif($input['input'] == 'map'): ?>
        <div class="col-md-6  col-12 <?php echo e(isset($input['col_md']) ? ' col-md-' . $input['col_md'] : ' '); ?>">
                <div>
                    <div class="form-group <?php echo e($input['map_address'] === null ? 'd-none' : ''); ?>">
                        <label for="commercial_number"><?php echo e(__('admin.address')); ?></label>
                        <div class="controls">
                            <input type="text" name="<?php echo e($input['map_address'] === null); ?> ? 'address' : $input['map_address']['name']" id="address" class="form-control"
                            placeholder="<?php echo e(isset($input['placeholder']) ? $input['placeholder'] : __('admin.enter_addresss')); ?>" 
                                    
                            <?php if(isset($input['validation']) && $input['validation'] != false): ?>
                                <?php $__currentLoopData = $input['validation']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $singleValidation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(isset($singleValidation['value'])): ?>
                                    <?php echo e($singleValidation['type'] . '=' . $singleValidation['value']); ?>

                                    <?php else: ?>
                                    <?php echo e($singleValidation['type']); ?>

                                    <?php endif; ?>
                                    data-validation-<?php echo e($singleValidation['type']); ?>-message='<?php echo e($singleValidation['message']); ?>'
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php elseif(!isset($input['validation'])): ?>
                                required
                                data-validation-required-message='<?php echo e(__('admin.this_field_is_required')); ?>' 
                            <?php endif; ?>

                            <?php if(isset($input['attributes'])): ?>
                                <?php $__currentLoopData = $input['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php echo e($key); ?>="<?php echo e($value); ?>"
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label ><?php echo e($input['text']); ?></label>
                    <input type="text" id="mapSearch" class="form-control" placeholder="<?php echo e(__('admin.search_in_map')); ?>" >

                    <div class="form-group">
                        <div id="map" style="width: 100%;height:250px;"></div>
                        <input type="hidden"  name="lat" id="lat" >
                        <input type="hidden"  name="lng" id="lng" >
                    </div>
                </div>
            </div>

        <?php elseif($input['input'] == 'custom'): ?>
            <?php echo $__env->yieldContent($inputName); ?>

        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php else: ?>
    <?php dd('the inputs array not found !'); ?>
<?php endif; ?><?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/shared/inputs/createInputs.blade.php ENDPATH**/ ?>