

<?php $__env->startSection('css'); ?>
  <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<div class="content-body">
  <!-- account setting page start -->
  <section id="page-account-settings">
      <div class="row">
          <!-- left menu section -->
          <div class="col-md-3 mb-2 mb-md-0 ">
              <ul class="nav nav-pills flex-column mt-md-0 mt-1 card card-body">

                <li class="nav-item">
                    <a class="nav-link d-flex py-75 active" id="account-pill-main" data-toggle="pill" href="#account-vertical-main" aria-expanded="true">
                        <i class="feather icon-settings mr-50 font-medium-3"></i>
                        <?php echo e(__('admin.app_setting')); ?>

                    </a>
                </li>
                <li class="nav-item" style="margin-top: 3px" >
                    <a class="nav-link d-flex py-75" id="account-pill-language" data-toggle="pill" href="#account-vertical-language" aria-expanded="true">
                        <i class="feather icon-settings mr-50 font-medium-3"></i>
                        <?php echo e(__('admin.language_setting')); ?>

                    </a>
                </li>
                <li class="nav-item" style="margin-top: 3px" >
                    <a class="nav-link d-flex py-75" id="account-pill-countries" data-toggle="pill" href="#account-vertical-countries" aria-expanded="true">
                        <i class="feather icon-settings mr-50 font-medium-3"></i>
                        <?php echo e(__('admin.countries_currencies')); ?>

                    </a>
                </li>
                <li class="nav-item" style="margin-top: 3px" >
                    <a class="nav-link d-flex py-75" id="account-pill-terms" data-toggle="pill" href="#account-vertical-terms" aria-expanded="false">
                        <i class="feather icon-edit-1 mr-50 font-medium-3"></i>
                        <?php echo e(__('admin.terms_and_conditions')); ?>

                    </a>
                </li>
                <li class="nav-item " style="margin-top: 3px">
                    <a class="nav-link d-flex py-75" id="account-pill-about" data-toggle="pill" href="#account-vertical-about" aria-expanded="false">
                        <i class="feather icon-edit-1 mr-50 font-medium-3"></i>
                        <?php echo e(__('admin.about_app')); ?>

                    </a>
                </li>
                <li class="nav-item " style="margin-top: 3px">
                    <a class="nav-link d-flex py-75" id="account-pill-privacy" data-toggle="pill" href="#account-vertical-privacy" aria-expanded="false">
                        <i class="feather icon-award mr-50 font-medium-3"></i>
                        <?php echo e(__('admin.Privacy_policy')); ?>

                    </a>
                </li>
                <li class="nav-item " style="margin-top: 3px">
                    <a class="nav-link d-flex py-75" id="account-pill-smtp" data-toggle="pill" href="#account-vertical-smtp" aria-expanded="false">
                        <i class="feather icon-mail mr-50 font-medium-3"></i>
                        <?php echo e(__('admin.email_data')); ?>

                    </a>
                </li>
                <li class="nav-item " style="margin-top: 3px">
                    <a class="nav-link d-flex py-75" id="account-pill-notifications" data-toggle="pill" href="#account-vertical-notifications" aria-expanded="false">
                        <i class="feather icon-bell mr-50 font-medium-3"></i>
                        <?php echo e(__('admin.notification_data')); ?>

                    </a>
                </li>
                <li class="nav-item " style="margin-top: 3px">
                    <a class="nav-link d-flex py-75" id="account-pill-api" data-toggle="pill" href="#account-vertical-api" aria-expanded="false">
                        <i class="feather icon-droplet mr-50 font-medium-3"></i>
                        <?php echo e(__('admin.api_data')); ?>

                    </a>
                </li>
                <li class="nav-item " style="margin-top: 3px">
                    <a class="nav-link d-flex py-75" id="account-pill-loyalty" data-toggle="pill" href="#account-vertical-loyalty" aria-expanded="false">
                        <i class="feather icon-award mr-50 font-medium-3"></i>
                        نقاط الولاء
                    </a>
                </li>

              </ul>
          </div>
          <!-- right content section -->
          <div class="col-md-9">
              <div class="card">
                  <div class="card-content">
                      <div class="card-body">
                          <div class="tab-content">

                              <div role="tabpanel" class="tab-pane active" id="account-vertical-main" aria-labelledby="account-pill-main" aria-expanded="true">
                                <form accept="<?php echo e(route('admin.settings.update')); ?>" method="post" enctype="multipart/form-data" class="form-horizontal" novalidate>
                                  <?php echo method_field('put'); ?>
                                  <?php echo csrf_field(); ?>
                                <div class="row">
                                  <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <div class="controls">
                                                <label for="account-name"><?php echo e(__('admin.the_name_of_the_application_in_arabic')); ?></label>
                                                <input type="text" class="form-control" name="name_ar" id="account-name" placeholder="<?php echo e(__('admin.the_name_of_the_application_in_arabic')); ?>" value="<?php echo e($data['name_ar']); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <div class="controls">
                                                <label for="account-name"><?php echo e(__('admin.the_name_of_the_application_in_english')); ?></label>
                                                <input type="text" class="form-control" name="name_en" id="account-name" placeholder="<?php echo e(__('admin.the_name_of_the_application_in_english')); ?>" value="<?php echo e($data['name_en']); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <div class="controls">
                                                <label for="account-name"><?php echo e(__('admin.email')); ?></label>
                                                <input type="email" class="form-control" name="email" id="account-name" placeholder="<?php echo e(__('admin.email')); ?>" value="<?php echo e($data['email']); ?>" data-validation-email-message="<?php echo e(__('admin.verify_the_email_format')); ?>" >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <div class="controls">
                                                <label for="account-name"><?php echo e(__('admin.phone')); ?></label>
                                                <input type="text" class="form-control" name="phone" id="account-name" placeholder="<?php echo e(__('admin.phone')); ?>" value="<?php echo e($data['phone']); ?>" minlength="10" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" data-validation-minlength-message="<?php echo e(__('admin.the_number_should_only_be_less_than_ten_numbers')); ?>" >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <div class="controls">
                                                <label for="account-name"><?php echo e(__('admin.whatts_app_number')); ?></label>
                                                <input type="text" class="form-control" name="whatsapp" id="account-name" placeholder="<?php echo e(__('admin.whatts_app_number')); ?>" value="<?php echo e($data['whatsapp']); ?>" minlength="10"  data-validation-minlength-message="<?php echo e(__('admin.the_number_should_only_be_less_than_ten_numbers')); ?>"  >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="account-name">is production </label>
                                            <div class="custom-control custom-switch custom-switch-success mr-2 mb-1">
                                                <input name="is_production" <?php echo e($data['is_production']  == '1' ? 'checked' : ''); ?>   type="checkbox" class="custom-control-input" id="customSwitch11">
                                                <label class="custom-control-label" for="customSwitch11">
                                                    <span class="switch-icon-left"><i class="feather icon-check"></i></span>
                                                    <span class="switch-icon-right"><i class="feather icon-check"></i></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="account-name">delivery registeration availability </label>
                                            <div class="custom-control custom-switch custom-switch-success mr-2 mb-1">
                                                <input name="registeration_availability" <?php echo e($data['registeration_availability']  == '1' ? 'checked' : ''); ?>   type="checkbox" class="custom-control-input" id="customSwitch12">
                                                <label class="custom-control-label" for="customSwitch12">
                                                    <span class="switch-icon-left"><i class="feather icon-check"></i></span>
                                                    <span class="switch-icon-right"><i class="feather icon-check"></i></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                      <div class="row">

                                        <div class="imgMontg col-12 col-lg-4 col-md-6 text-center">
                                            <div class="dropBox">
                                                <div class="textCenter d-flex flex-column">
                                                    <div class="imagesUploadBlock">
                                                        <label class="uploadImg">
                                                            <span><i class="feather icon-image"></i></span>
                                                            <input type="file" accept="image/*" name="logo" class="imageUploader">
                                                        </label>
                                                        <div class="uploadedBlock">
                                                            <?php if(isset($imageSettings['logo']) && $imageSettings['logo']->getFirstMediaUrl('logo')): ?>
                                                                <img src="<?php echo e($imageSettings['logo']->getFirstMediaUrl('logo')); ?>">
                                                            <?php else: ?>
                                                                <img src="<?php echo e(asset('storage/images/settings/default.png')); ?>">
                                                            <?php endif; ?>
                                                            <button class="close"><i class="feather icon-trash-2"></i></button>
                                                        </div>
                                                      </div>
                                                      <span><?php echo e(__('admin.logo_image')); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="imgMontg col-12 col-lg-4 col-md-6 text-center">
                                            <div class="dropBox">
                                                <div class="textCenter d-flex flex-column">
                                                    <div class="imagesUploadBlock">
                                                        <label class="uploadImg">
                                                            <span><i class="feather icon-image"></i></span>
                                                            <input type="file" accept="image/*" name="fav_icon" class="imageUploader">
                                                        </label>
                                                        <div class="uploadedBlock">
                                                            <?php if(isset($imageSettings['fav_icon']) && $imageSettings['fav_icon']->getFirstMediaUrl('fav_icon')): ?>
                                                                <img src="<?php echo e($imageSettings['fav_icon']->getFirstMediaUrl('fav_icon')); ?>">
                                                            <?php else: ?>
                                                                <img src="<?php echo e(asset('storage/images/settings/default.png')); ?>">
                                                            <?php endif; ?>
                                                            <button class="close"><i class="feather icon-trash-2"></i></button>
                                                        </div>
                                                      </div>
                                                      <span><?php echo e(__('admin.fav_icon_image')); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="imgMontg col-12 col-lg-4 col-md-6 text-center">
                                            <div class="dropBox">
                                                <div class="textCenter d-flex flex-column">
                                                    <div class="imagesUploadBlock">
                                                        <label class="uploadImg">
                                                            <span><i class="feather icon-image"></i></span>
                                                            <input type="file" accept="image/*" name="default_user" class="imageUploader">
                                                        </label>
                                                        <div class="uploadedBlock">
                                                            <?php if(isset($imageSettings['default_user']) && $imageSettings['default_user']->getFirstMediaUrl('default_user')): ?>
                                                                <img src="<?php echo e($imageSettings['default_user']->getFirstMediaUrl('default_user')); ?>">
                                                            <?php else: ?>
                                                                <img src="<?php echo e(asset('storage/images/users/default.png')); ?>">
                                                            <?php endif; ?>
                                                            <button class="close"><i class="feather icon-trash-2"></i></button>
                                                        </div>
                                                      </div>
                                                      <span><?php echo e(__('admin.virtual_user_image')); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="imgMontg col-12 col-lg-4 col-md-6 text-center">
                                            <div class="dropBox">
                                                <div class="textCenter d-flex flex-column">
                                                    <div class="imagesUploadBlock">
                                                        <label class="uploadImg">
                                                            <span><i class="feather icon-image"></i></span>
                                                            <input type="file" accept="image/*" name="profile_cover" class="imageUploader">
                                                        </label>
                                                        <div class="uploadedBlock">
                                                            <?php if(isset($imageSettings['profile_cover']) && $imageSettings['profile_cover']->getFirstMediaUrl('profile_cover')): ?>
                                                                <img src="<?php echo e($imageSettings['profile_cover']->getFirstMediaUrl('profile_cover')); ?>">
                                                            <?php else: ?>
                                                                <img src="<?php echo e(asset('storage/images/settings/default.png')); ?>">
                                                            <?php endif; ?>
                                                            <button class="close"><i class="feather icon-trash-2"></i></button>
                                                        </div>
                                                      </div>
                                                      <span><?php echo e(__('admin.profile_cover')); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                      </div>

                                    </div>
                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.saving_changes')); ?></button>
                                        <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                    </div>
                                </div>
                                </form>
                              </div>


                              <div role="tabpanel" class="tab-pane" id="account-vertical-language" aria-labelledby="account-pill-language" aria-expanded="false">
                                <form accept="<?php echo e(route('admin.settings.update')); ?>" method="post" enctype="multipart/form-data">
                                    <?php echo method_field('put'); ?>
                                    <?php echo csrf_field(); ?>
                                    <div class="row">

                                        <div class="col-12 col-md-12">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.supported_languages')); ?></label>
                                                    <select name="locales[]" class="form-control select2" multiple="">
                                                        <?php $__currentLoopData = config('available-locales'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($key); ?>"
                                                                <?php if(in_array($key,json_decode($data['locales']))): ?>
                                                                    selected
                                                                <?php endif; ?> >
                                                                <?php echo e($language['native']); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-12">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.rtl_languages')); ?></label>
                                                    <select name="rtl_locales[]" class="form-control select2" multiple="">
                                                        <?php $__currentLoopData = config('available-locales'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($key); ?>"
                                                                    <?php if(in_array($key,json_decode($data['rtl_locales']))): ?>
                                                                    selected
                                                                <?php endif; ?>>
                                                                <?php echo e($language['native']); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-12">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.default_language')); ?></label>
                                                    <select name="default_locale" class="form-control select2">
                                                        <?php $__currentLoopData = config('available-locales'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($key); ?>"
                                                                    <?php if($data['default_locale'] == $key): ?>
                                                                    selected
                                                                <?php endif; ?>>
                                                                <?php echo e($language['native']); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                          <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.saving_changes')); ?></button>
                                          <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                      </div>
                                    </div>
                                </form>
                              </div>

                              <div role="tabpanel" class="tab-pane" id="account-vertical-countries" aria-labelledby="account-pill-countries" aria-expanded="false">
                                <form accept="<?php echo e(route('admin.settings.update')); ?>" method="post" enctype="multipart/form-data">
                                    <?php echo method_field('put'); ?>
                                    <?php echo csrf_field(); ?>
                                    <div class="row">

                                        <div class="col-12 col-md-12">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.supported_countries')); ?></label>
                                                    <select name="countries[]" class="form-control select2" multiple="">
                                                        <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($country->id); ?>"
                                                                <?php if(in_array($country->id,json_decode($data['countries']))): ?>
                                                                    selected
                                                                <?php endif; ?> >
                                                                <?php echo e($country->name); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-12">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.default_country')); ?></label>
                                                    <select name="default_country" class="form-control select2">
                                                        <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($country->id); ?>"
                                                                    <?php if($data['default_country'] == $country->id): ?>
                                                                    selected
                                                                <?php endif; ?>>
                                                                <?php echo e($country->name); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-12">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.supported_currencies')); ?></label>
                                                    <select name="currencies[]" class="form-control select2" multiple="">
                                                        <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($country->currency_code); ?>"
                                                                <?php if(in_array($country->currency_code,json_decode($data['currencies']))): ?>
                                                                    selected
                                                                <?php endif; ?> >
                                                                <?php echo e($country->currency); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-12">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.default_currency')); ?></label>
                                                    <select name="default_currency" class="form-control select2">
                                                        <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($country->currency_code); ?>"
                                                                    <?php if($data['default_currency'] == $country->currency_code): ?>
                                                                    selected
                                                                <?php endif; ?>>
                                                                <?php echo e($country->currency); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-12">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.default_currency')); ?></label>

                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-12">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="vat_amount"><?php echo e(__('admin.vat_amount')); ?> (%)</label>
                                                    <input type="number" class="form-control" name="vat_amount" id="vat_amount"
                                                        placeholder="<?php echo e(__('admin.vat_amount')); ?>"
                                                        value="<?php echo e($data['vat_amount'] ?? 15); ?>"
                                                        min="0" max="100" step="0.01" required
                                                        data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                                    <small class="form-text text-muted"><?php echo e(__('admin.vat_amount_help')); ?></small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                          <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.saving_changes')); ?></button>
                                          <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                      </div>
                                    </div>
                                </form>
                              </div>

                              <div role="tabpanel" class="tab-pane" id="account-vertical-terms" aria-labelledby="account-pill-terms" aria-expanded="false">
                                <form accept="<?php echo e(route('admin.settings.update')); ?>" method="post" enctype="multipart/form-data">
                                    <?php echo method_field('put'); ?>
                                    <?php echo csrf_field(); ?>
                                    <div class="row">

                                        <div class="col-12">
                                            <ul class="nav nav-tabs  mb-3">
                                                <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li class="nav-item">
                                                        <a class="nav-link <?php if($loop->first): ?> active <?php endif; ?>"  data-toggle="pill" href="#first_<?php echo e($lang); ?>" aria-expanded="true"><?php echo e(__('admin.data')); ?> <?php echo e($lang); ?></a>
                                                    </li>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                        </div>

                                        <div class="tab-content">
                                            <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div role="tabpanel" class="tab-pane fade <?php if($loop->first): ?> show active <?php endif; ?> " id="first_<?php echo e($lang); ?>" aria-labelledby="first_<?php echo e($lang); ?>" aria-expanded="true">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div class="controls">
                                                                <label for="account-name"><?php echo e(__('admin.conditions_and_conditions')); ?> <?php echo e($lang); ?></label>
                                                                <textarea class="form-control" name="terms_<?php echo e($lang); ?>" id="terms_<?php echo e($lang); ?>_editor" cols="30" rows="10" placeholder="<?php echo e(__('admin.conditions_and_conditions')); ?> <?php echo e($lang); ?>"><?php echo e($data['terms_'.$lang]??''); ?></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                          <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.saving_changes')); ?></button>
                                          <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                      </div>
                                    </div>
                                </form>
                              </div>

                              <div role="tabpanel" class="tab-pane" id="account-vertical-about" aria-labelledby="account-pill-about" aria-expanded="false">
                                <form accept="<?php echo e(route('admin.settings.update')); ?>" method="post" enctype="multipart/form-data">
                                    <?php echo method_field('put'); ?>
                                    <?php echo csrf_field(); ?>
                                    <div class="row">

                                        <div class="col-12">
                                            <ul class="nav nav-tabs  mb-3">
                                                <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li class="nav-item">
                                                        <a class="nav-link <?php if($loop->first): ?> active <?php endif; ?>"  data-toggle="pill" href="#about_<?php echo e($lang); ?>" aria-expanded="true"><?php echo e(__('admin.data')); ?> <?php echo e($lang); ?></a>
                                                    </li>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                        </div>

                                        <div class="tab-content">
                                            <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div role="tabpanel" class="tab-pane fade <?php if($loop->first): ?> show active <?php endif; ?> " id="about_<?php echo e($lang); ?>" aria-labelledby="first_<?php echo e($lang); ?>" aria-expanded="true">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div class="controls">
                                                                <label for="account-name"><?php echo e(__('admin.about_the_application')); ?> <?php echo e($lang); ?></label>
                                                                <textarea class="form-control" name="about_<?php echo e($lang); ?>" id="about_<?php echo e($lang); ?>_editor" cols="30" rows="10" placeholder="<?php echo e(__('admin.about_the_application')); ?> <?php echo e($lang); ?>"><?php echo e($data['about_'.$lang]??''); ?></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                          <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.saving_changes')); ?></button>
                                          <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                      </div>
                                    </div>
                                </form>
                              </div>

                              <div role="tabpanel" class="tab-pane" id="account-vertical-privacy" aria-labelledby="account-pill-privacy" aria-expanded="false">
                                <form accept="<?php echo e(route('admin.settings.update')); ?>" method="post" enctype="multipart/form-data">
                                    <?php echo method_field('put'); ?>
                                    <?php echo csrf_field(); ?>
                                    <div class="row">

                                        <div class="col-12">
                                            <ul class="nav nav-tabs  mb-3">
                                                <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li class="nav-item">
                                                        <a class="nav-link <?php if($loop->first): ?> active <?php endif; ?>"  data-toggle="pill" href="#privacy_<?php echo e($lang); ?>" aria-expanded="true"><?php echo e(__('admin.data')); ?> <?php echo e($lang); ?></a>
                                                    </li>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                        </div>

                                        <div class="tab-content">
                                            <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div role="tabpanel" class="tab-pane fade <?php if($loop->first): ?> show active <?php endif; ?> " id="privacy_<?php echo e($lang); ?>" aria-labelledby="first_<?php echo e($lang); ?>" aria-expanded="true">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div class="controls">
                                                                <label for="account-name"><?php echo e(__('admin.privacy_policy')); ?> <?php echo e($lang); ?></label>
                                                                <textarea class="form-control" name="privacy_<?php echo e($lang); ?>" id="privacy_<?php echo e($lang); ?>_editor" cols="30" rows="10" placeholder="<?php echo e(__('admin.privacy_policy')); ?> <?php echo e($lang); ?>"><?php echo e($data['privacy_'.$lang]??''); ?></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                          <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.saving_changes')); ?></button>
                                          <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                      </div>
                                    </div>
                                </form>
                              </div>

                              <div role="tabpanel" class="tab-pane" id="account-vertical-smtp" aria-labelledby="account-pill-smtp" aria-expanded="false">
                                <form accept="<?php echo e(route('admin.settings.update')); ?>" method="post" enctype="multipart/form-data">
                                    <?php echo method_field('put'); ?>
                                    <?php echo csrf_field(); ?>
                                    <div class="row">

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.user_name')); ?></label>
                                                    <input type="text" class="form-control" name="smtp_user_name" id="account-name" placeholder="<?php echo e(__('admin.user_name')); ?>" value="<?php echo e($data['smtp_user_name']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.password')); ?></label>
                                                    <input type="password" class="form-control" name="smtp_password" id="account-name" placeholder="<?php echo e(__('admin.password')); ?>" value="<?php echo e($data['smtp_password']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.email_Sender')); ?></label>
                                                    <input type="text" class="form-control" name="smtp_mail_from" id="account-name" placeholder="<?php echo e(__('admin.email_Sender')); ?>" value="<?php echo e($data['smtp_mail_from']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.the_sender_name')); ?></label>
                                                    <input type="text" class="form-control" name="smtp_sender_name" id="account-name" placeholder="<?php echo e(__('admin.the_sender_name')); ?>" value="<?php echo e($data['smtp_sender_name']); ?>">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.the_nouns_al')); ?></label>
                                                    <input type="text" class="form-control" name="smtp_host" id="account-name" placeholder="<?php echo e(__('admin.the_nouns_al')); ?>" value="<?php echo e($data['smtp_host']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.encryption_type')); ?></label>
                                                    <input type="text" class="form-control" name="smtp_encryption" id="account-name" placeholder="<?php echo e(__('admin.encryption_type')); ?>" value="<?php echo e($data['smtp_encryption']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.Port_number')); ?></label>
                                                    <input type="number" class="form-control" name="smtp_port" id="account-name" placeholder="<?php echo e(__('admin.Port_number')); ?>" value="<?php echo e($data['smtp_port']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                          <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.saving_changes')); ?></button>
                                          <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                      </div>
                                    </div>
                                </form>
                              </div>

                              <div role="tabpanel" class="tab-pane" id="account-vertical-notifications" aria-labelledby="account-pill-notifications" aria-expanded="false">
                                <form accept="<?php echo e(route('admin.settings.update')); ?>" method="post" enctype="multipart/form-data">
                                    <?php echo method_field('put'); ?>
                                    <?php echo csrf_field(); ?>
                                    <div class="row">

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.server_key')); ?></label>
                                                    <input type="text" class="form-control" name="firebase_key" id="account-name" placeholder="<?php echo e(__('admin.server_key')); ?>" value="<?php echo e($data['firebase_key']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.sender_identification')); ?></label>
                                                    <input type="text" class="form-control" name="firebase_sender_id" id="account-name" placeholder="<?php echo e(__('admin.sender_identification')); ?>" value="<?php echo e($data['firebase_sender_id']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                          <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.saving_changes')); ?></button>
                                          <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                      </div>
                                    </div>
                                </form>
                              </div>

                              <div role="tabpanel" class="tab-pane" id="account-vertical-api" aria-labelledby="account-pill-api" aria-expanded="false">
                                <form accept="<?php echo e(route('admin.settings.update')); ?>" method="post" enctype="multipart/form-data">
                                    <?php echo method_field('put'); ?>
                                    <?php echo csrf_field(); ?>
                                    <div class="row">

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.live_chat')); ?></label>
                                                    <input type="text" class="form-control" name="live_chat" id="account-name" placeholder="<?php echo e(__('admin.live_chat')); ?>" value="<?php echo e($data['live_chat']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.google_analytics')); ?></label>
                                                    <input type="text" class="form-control" name="google_analytics" id="account-name" placeholder="<?php echo e(__('admin.google_analytics')); ?>" value="<?php echo e($data['google_analytics']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="account-name"><?php echo e(__('admin.google_places')); ?></label>
                                                    <input type="text" class="form-control" name="google_places" id="account-name" placeholder="<?php echo e(__('admin.google_places')); ?>" value="<?php echo e($data['google_places']); ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                          <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.saving_changes')); ?></button>
                                          <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                      </div>
                                    </div>
                                </form>
                              </div>

                              <div role="tabpanel" class="tab-pane" id="account-vertical-loyalty" aria-labelledby="account-pill-loyalty" aria-expanded="false">
                                <form accept="<?php echo e(route('admin.settings.update')); ?>" method="post" enctype="multipart/form-data">
                                    <?php echo method_field('put'); ?>
                                    <?php echo csrf_field(); ?>
                                    <div class="row">

                                        <div class="col-12">
                                            <h4 class="mb-3">إعدادات نقاط الولاء</h4>
                                            <p class="text-muted mb-4">تحكم في آلية عمل نقاط الولاء في التطبيق</p>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <label for="loyalty_points_enabled">تفعيل نظام نقاط الولاء</label>
                                                <div class="custom-control custom-switch custom-switch-success mr-2 mb-1">
                                                    <input name="loyalty_points_enabled" <?php echo e($data['loyalty_points_enabled'] == '1' ? 'checked' : ''); ?> type="checkbox" class="custom-control-input" id="loyalty_points_enabled">
                                                    <label class="custom-control-label" for="loyalty_points_enabled">
                                                        <span class="switch-icon-left"><i class="feather icon-check"></i></span>
                                                        <span class="switch-icon-right"><i class="feather icon-x"></i></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="loyalty_points_earn_rate">عدد النقاط المكتسبة لكل ريال</label>
                                                    <input type="number" class="form-control" name="loyalty_points_earn_rate" id="loyalty_points_earn_rate"
                                                        placeholder="1" value="<?php echo e($data['loyalty_points_earn_rate']); ?>"
                                                        min="0" step="0.01" required>
                                                    <small class="form-text text-muted">مثال: 1 = نقطة واحدة لكل ريال مدفوع</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="loyalty_points_redeem_rate">قيمة النقطة الواحدة بالريال</label>
                                                    <input type="number" class="form-control" name="loyalty_points_redeem_rate" id="loyalty_points_redeem_rate"
                                                        placeholder="1" value="<?php echo e($data['loyalty_points_redeem_rate']); ?>"
                                                        min="0" step="0.01" required>
                                                    <small class="form-text text-muted">مثال: 1 = النقطة الواحدة تساوي ريال واحد</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="loyalty_points_min_redeem">الحد الأدنى لاستخدام النقاط</label>
                                                    <input type="number" class="form-control" name="loyalty_points_min_redeem" id="loyalty_points_min_redeem"
                                                        placeholder="10" value="<?php echo e($data['loyalty_points_min_redeem']); ?>"
                                                        min="1" required>
                                                    <small class="form-text text-muted">أقل عدد نقاط يمكن استخدامها في الدفع</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6">
                                            <div class="form-group">
                                                <div class="controls">
                                                    <label for="loyalty_points_max_redeem_percentage">الحد الأقصى لاستخدام النقاط (%)</label>
                                                    <input type="number" class="form-control" name="loyalty_points_max_redeem_percentage" id="loyalty_points_max_redeem_percentage"
                                                        placeholder="50" value="<?php echo e($data['loyalty_points_max_redeem_percentage']); ?>"
                                                        min="1" max="100" required>
                                                    <small class="form-text text-muted">أقصى نسبة من قيمة الطلب يمكن دفعها بالنقاط</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12">
                                            <div class="alert alert-info">
                                                <h5><i class="feather icon-info mr-1"></i> ملاحظات مهمة:</h5>
                                                <ul class="mb-0">
                                                    <li>النقاط تُكتسب فقط عند الدفع الفعلي (إلكتروني، تحويل بنكي، أو رصيد المحفظة)</li>
                                                    <li>النقاط المستخدمة في الدفع لا تكسب نقاط جديدة</li>
                                                    <li>يتم حساب النقاط المكتسبة بناءً على المبلغ المدفوع فعلياً</li>
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                          <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.saving_changes')); ?></button>
                                          <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                      </div>
                                    </div>
                                </form>
                              </div>

                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>
  <!-- account setting page end -->

</div>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')); ?>"></script>
    <script src="https://cdn.ckeditor.com/4.16.2/full-all/ckeditor.js"></script>
    <script>
        <?php $__currentLoopData = languages(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            CKEDITOR.replace( 'about_<?php echo e($lang); ?>_editor' );
            CKEDITOR.replace( 'terms_<?php echo e($lang); ?>_editor' );
            CKEDITOR.replace( 'privacy_<?php echo e($lang); ?>_editor' );
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </script>
  
    <?php echo $__env->make('admin.shared.addImage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
  
<?php $__env->stopSection(); ?>


<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/settings/index.blade.php ENDPATH**/ ?>