
    <div class="sidenav-overlay"></div>
    <div class="drag-target"></div>
    
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/switchery/0.8.2/switchery.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>


    <script src="<?php echo e(asset('admin/active.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/assets/js/flatpickr.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/vendors.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/core/app-menu.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/core/app.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/components.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/toastr.min.js')); ?>"></script>
    <script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        // input date js
        var $list = $(":input[type='date']");
        $(window).on('load', function () {
            if ($($list).length > 0) {
                $(document).find($list).addClass("custom-input-date");
                $(document).find($list).parents(".controls").addClass("parent-input-date");
                $($list).prop("type", "text");
                flatpickr($list, {
                    disableMobile: true,
                    // minDate: "today",
                });
            }
        })
        $(document).ready(function () {
            $(".select2").select2();  
        });
    </script>

    <?php echo $__env->yieldContent('js'); ?>

    <?php if (isset($component)) { $__componentOriginal07ddb047ae3451301e07dda88a33c35d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal07ddb047ae3451301e07dda88a33c35d = $attributes; } ?>
<?php $component = App\View\Components\Admin\Alert::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.alert'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Admin\Alert::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal07ddb047ae3451301e07dda88a33c35d)): ?>
<?php $attributes = $__attributesOriginal07ddb047ae3451301e07dda88a33c35d; ?>
<?php unset($__attributesOriginal07ddb047ae3451301e07dda88a33c35d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal07ddb047ae3451301e07dda88a33c35d)): ?>
<?php $component = $__componentOriginal07ddb047ae3451301e07dda88a33c35d; ?>
<?php unset($__componentOriginal07ddb047ae3451301e07dda88a33c35d); ?>
<?php endif; ?>
    
</body>
</html><?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/layout/partial/footer.blade.php ENDPATH**/ ?>