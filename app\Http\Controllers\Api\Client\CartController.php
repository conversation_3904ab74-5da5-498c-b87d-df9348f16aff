<?php

namespace App\Http\Controllers\Api\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Cart\AddToCartRequest;
use App\Http\Requests\Api\Cart\RemoveFromCartRequest;
use App\Http\Requests\Api\Cart\UpdateCartItemRequest;
use App\Http\Requests\Api\Cart\ApplyCouponRequest;
use App\Http\Requests\Api\Cart\ApplyLoyaltyPointsRequest;
use App\Http\Resources\Api\CartResource;
use App\Services\CartService;
use App\Services\Responder;
use App\Traits\ResponseTrait;

class CartController extends Controller {
  use ResponseTrait;

  /**
   * @var CartService
   */
  protected $cartService;

  /**
   * CartController constructor.
   *
   * @param CartService $cartService
   */
  public function __construct(CartService $cartService)
  {
    $this->cartService = $cartService;
  }

  /**
   * Get the current user's cart
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function getCart()
  {
      $user = auth()->user();
      $cart = $this->cartService->getCart($user);

      if (!$cart->items()->count() > 0) {
          return Responder::success([], ['message' => __('apis.cart_is_empty')]);
      }

      $cart->load('items.product');
      return Responder::success(new CartResource($cart));
  }

  /**
   * Add a product to the cart
   *
   * @param \App\Http\Requests\Api\Cart\AddToCartRequest $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function addToCart(AddToCartRequest $request)
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->addToCart($user, $request->validated());

      return Responder::success(new CartResource($cart), ['message' => __('apis.product_added_to_cart')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Update a cart item quantity
   *
   * @param \App\Http\Requests\Api\Cart\UpdateCartItemRequest $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function updateCartItem(UpdateCartItemRequest $request)
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->updateCartItem($user, $request->validated());

      return Responder::success(new CartResource($cart), ['message' => __('apis.cart_updated')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Remove a product from the cart
   *
   * @param \App\Http\Requests\Api\Cart\RemoveFromCartRequest $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function removeFromCart(RemoveFromCartRequest $request)
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->removeFromCart($user, $request->validated()['cart_item_id']);

      return Responder::success(new CartResource($cart), ['message' => __('apis.product_removed_from_cart')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Clear the cart
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function clearCart()
  {
    try {
      $user = auth()->user();
      $this->cartService->clearCart($user);

      return Responder::success([], ['message' => __('apis.cart_cleared')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Apply coupon to cart
   *
   * @param \App\Http\Requests\Api\Cart\ApplyCouponRequest $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function applyCoupon(ApplyCouponRequest $request)
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->applyCoupon($user, $request->validated()['coupon_code']);

      return Responder::success(new CartResource($cart), ['message' => __('apis.coupon_applied')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Apply loyalty points to cart
   *
   * @param \App\Http\Requests\Api\Cart\ApplyLoyaltyPointsRequest $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function applyLoyaltyPoints(ApplyLoyaltyPointsRequest $request)
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->applyLoyaltyPoints($user, $request->validated()['points']);

      return Responder::success(new CartResource($cart), ['message' => __('apis.loyalty_points_applied')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Remove coupon from cart
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function removeCoupon()
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->getCart($user);

      $cart->update([
        'coupon_code' => null,
        'discount_amount' => 0,
      ]);

      return Responder::success(new CartResource($cart->load(['items.item', 'provider'])), ['message' => __('apis.coupon_removed')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Remove loyalty points from cart
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function removeLoyaltyPoints()
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->getCart($user);

      $cart->update(['loyalty_points_used' => 0]);

      return Responder::success(new CartResource($cart->load(['items.item', 'provider'])), ['message' => __('apis.loyalty_points_removed')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }
}
