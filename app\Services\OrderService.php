<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Cart;
use App\Models\User;
use App\Models\Address;
use App\Models\Product;
use App\Models\Provider;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class OrderService
{
    protected $loyaltyPointsService;

    public function __construct(LoyaltyPointsService $loyaltyPointsService)
    {
        $this->loyaltyPointsService = $loyaltyPointsService;
    }

    /**
     * Create order from cart
     *
     * @param User $user
     * @param array $data
     * @return Order|array
     * @throws \Exception
     */
    public function createOrderFromCart(User $user, array $data)
    {
        return DB::transaction(function () use ($user, $data) {
            $cart = $user->cart;

            if (!$cart || $cart->items()->count() === 0) {
                throw new \Exception('Cart is empty');
            }

            // Validate order data
            $this->validateOrderData($cart, $data);

            // Check wallet balance if payment method is wallet
            if ($data['payment_method'] === 'wallet') {
                $walletValidation = $this->validateWalletBalance($user, $cart->total);
                if (!$walletValidation['valid']) {
                    return $walletValidation;
                }
            }

            // Check and reserve product quantities
            $this->checkAndReserveProductQuantities($cart);

            // Create the order
            $order = $this->createOrder($user, $cart, $data);

            // Create order items
            $this->createOrderItems($order, $cart);

            // Process payment based on method
            $paymentResult = $this->processPayment($order, $data);

            if ($paymentResult['success']) {
                // Clear the cart
                $cart->items()->delete();
                $cart->delete();

                return $order->load(['items.item', 'address', 'provider']);
            } else {
                // Rollback product quantities
                $this->rollbackProductQuantities($order);
                throw new \Exception($paymentResult['message']);
            }
        });
    }

    /**
     * Validate order data
     */
    private function validateOrderData(Cart $cart, array $data)
    {
        // Validate address
        $address = Address::where('id', $data['address_id'])
            ->where('user_id', $cart->user_id)
            ->first();

        if (!$address) {
            throw new \Exception('Invalid address selected');
        }

        // For services with home booking, check if address is in provider's city
        if ($cart->hasServices() && $data['booking_type'] === 'home') {
            $provider = $cart->provider;
            if ($provider && $address->city_id !== $provider->city_id) {
                throw new \Exception('Address must be in the service provider\'s city for home booking');
            }
        }

        // Validate scheduled time for services
        if ($cart->hasServices() && isset($data['scheduled_at'])) {
            $scheduledTime = Carbon::parse($data['scheduled_at']);
            if ($scheduledTime->isPast()) {
                throw new \Exception('Scheduled time cannot be in the past');
            }
        }

        // Check provider availability for services
        if ($cart->hasServices()) {
            $provider = Provider::find($cart->provider_id);
            if (!$provider || !$provider->accept_orders || $provider->status !== 'active') {
                throw new \Exception('Service provider is currently unavailable');
            }
        }
    }

    /**
     * Validate wallet balance
     */
    private function validateWalletBalance(User $user, $totalAmount)
    {
        if ($user->wallet_balance < $totalAmount) {
            return [
                'valid' => false,
                'requires_payment_method_change' => true,
                'message' => 'Wallet balance is insufficient. Please choose another payment method.',
                'wallet_balance' => $user->wallet_balance,
                'required_amount' => $totalAmount,
            ];
        }

        return ['valid' => true];
    }

    /**
     * Check and reserve product quantities
     */
    private function checkAndReserveProductQuantities(Cart $cart)
    {
        foreach ($cart->items as $cartItem) {
            if ($cartItem->isProduct()) {
                $product = $cartItem->item;

                if ($product->quantity < $cartItem->quantity) {
                    throw new \Exception("Insufficient stock for product: {$product->name}");
                }

                // Reserve quantity (freeze until payment completion)
                $product->decrement('quantity', $cartItem->quantity);
            }
        }
    }

    /**
     * Rollback product quantities
     */
    private function rollbackProductQuantities(Order $order)
    {
        foreach ($order->items as $orderItem) {
            if ($orderItem->isProduct()) {
                $product = $orderItem->item;
                if ($product) {
                    $product->increment('quantity', $orderItem->quantity);
                }
            }
        }
    }

    /**
     * Create order from cart
     */
    private function createOrder(User $user, Cart $cart, array $data)
    {
        $orderData = [
            'order_number' => Order::generateOrderNumber(),
            'user_id' => $user->id,
            'provider_id' => $cart->provider_id,
            'address_id' => $data['address_id'],
            'status' => 'pending_payment',
            'payment_method' => $data['payment_method'],
            'payment_status' => 'pending',
            'subtotal' => $cart->subtotal,
            'discount_amount' => $cart->discount_amount,
            'coupon_code' => $cart->coupon_code,
            'booking_fee' => $cart->booking_fee,
            'delivery_fee' => $cart->delivery_fee ?? 0,
            'total' => $cart->total,
            'loyalty_points_used' => $cart->loyalty_points_used,
            'booking_type' => $data['booking_type'] ?? null,
            'delivery_type' => $data['delivery_type'] ?? 'normal',
            'scheduled_at' => isset($data['scheduled_at']) ? Carbon::parse($data['scheduled_at']) : null,
        ];

        return Order::create($orderData);
    }

    /**
     * Create order items from cart items
     */
    private function createOrderItems(Order $order, Cart $cart)
    {
        foreach ($cart->items as $cartItem) {
            OrderItem::create([
                'order_id' => $order->id,
                'item_type' => $cartItem->item_type,
                'item_id' => $cartItem->item_id,
                'name' => $cartItem->item->name,
                'quantity' => $cartItem->quantity,
                'price' => $cartItem->price,
                'total' => $cartItem->total,
                'options' => $cartItem->options,
            ]);
        }
    }

    /**
     * Process payment based on method
     */
    private function processPayment(Order $order, array $data)
    {
        switch ($data['payment_method']) {
            case 'wallet':
                return $this->processWalletPayment($order);

            case 'bank_transfer':
                return $this->processBankTransferPayment($order, $data);

            case 'credit_card':
            case 'mada':
            case 'apple_pay':
                return $this->processElectronicPayment($order, $data);

            default:
                return ['success' => false, 'message' => 'Invalid payment method'];
        }
    }

    /**
     * Process wallet payment
     */
    private function processWalletPayment(Order $order)
    {
        $user = $order->user;

        if ($user->wallet_balance >= $order->total) {
            // Deduct from wallet
            $user->decrement('wallet_balance', $order->total);

            // Update order status
            $order->update([
                'payment_status' => 'paid',
                'status' => 'processing',
                'payment_reference' => 'WALLET-' . time(),
            ]);

            // Process loyalty points
            $this->processLoyaltyPoints($order, 'wallet');

            return ['success' => true, 'message' => 'Payment successful'];
        }

        return ['success' => false, 'message' => 'Insufficient wallet balance'];
    }

    /**
     * Process bank transfer payment
     */
    private function processBankTransferPayment(Order $order, array $data)
    {
        // For bank transfer, order stays pending until admin confirms
        $order->update([
            'status' => 'pending_payment',
            'payment_reference' => 'BANK-' . $order->order_number,
        ]);

        return [
            'success' => true,
            'message' => 'Order created successfully. Please complete bank transfer.',
            'requires_bank_transfer' => true,
            'bank_details' => $this->getBankTransferDetails(),
        ];
    }

    /**
     * Process electronic payment (credit card, mada, apple pay)
     */
    private function processElectronicPayment(Order $order, array $data)
    {
        // This would integrate with payment gateway
        // For now, we'll simulate the process

        return [
            'success' => true,
            'message' => 'Redirect to payment gateway',
            'requires_payment_gateway' => true,
            'payment_url' => $this->generatePaymentGatewayUrl($order),
        ];
    }

    /**
     * Process loyalty points after successful payment
     */
    private function processLoyaltyPoints(Order $order, string $paymentMethod)
    {
        $loyaltyResult = $this->loyaltyPointsService->processOrderLoyaltyPoints(
            $order->user,
            $order->total,
            $order->loyalty_points_used,
            $paymentMethod
        );

        $order->update([
            'loyalty_points_earned' => $loyaltyResult['points_earned'],
        ]);
    }

    /**
     * Get bank transfer details
     */
    private function getBankTransferDetails()
    {
        return [
            'bank_name' => 'البنك الأهلي السعودي',
            'beneficiary_name' => 'شركة سوريسو للتقنية',
            'account_number' => '**********',
            'iban' => 'SA********************12',
        ];
    }

    /**
     * Generate payment gateway URL
     */
    private function generatePaymentGatewayUrl(Order $order)
    {
        // This would generate actual payment gateway URL
        return "https://payment-gateway.com/pay/{$order->order_number}";
    }

    /**
     * Confirm payment (called by payment gateway webhook or admin)
     */
    public function confirmPayment(Order $order, string $paymentReference = null)
    {
        return DB::transaction(function () use ($order, $paymentReference) {
            $order->update([
                'payment_status' => 'paid',
                'status' => 'processing',
                'payment_reference' => $paymentReference ?? $order->payment_reference,
            ]);

            // Process loyalty points for electronic payments
            if (in_array($order->payment_method, ['credit_card', 'mada', 'apple_pay'])) {
                $this->processLoyaltyPoints($order, 'electronic');
            }

            return $order;
        });
    }

    /**
     * Cancel order and restore product quantities
     */
    public function cancelOrder(Order $order, string $reason = null)
    {
        return DB::transaction(function () use ($order, $reason) {
            if (!$order->canBeCancelled()) {
                throw new \Exception('Order cannot be cancelled at this stage');
            }

            // Restore product quantities
            $this->rollbackProductQuantities($order);

            // Refund wallet if paid via wallet
            if ($order->payment_method === 'wallet' && $order->payment_status === 'paid') {
                $order->user->increment('wallet_balance', $order->total);
            }

            // Restore loyalty points if used
            if ($order->loyalty_points_used > 0) {
                $order->user->increment('loyalty_points', $order->loyalty_points_used);
            }

            $order->update([
                'status' => 'cancelled',
                'cancellation_reason' => $reason,
            ]);

            return $order;
        });
    }
}

            // Create the order
            $order = $this->createOrder($user, $cart, $data);

            // Create order items
            $this->createOrderItems($order, $cart);

            // Process payment based on method
            $paymentResult = $this->processPayment($order, $data);

            if ($paymentResult['success']) {
                // Clear the cart
                $cart->items()->delete();
                $cart->delete();

                return $order->load(['items.item', 'address', 'provider']);
            } else {
                // Rollback product quantities
                $this->rollbackProductQuantities($order);
                throw new \Exception($paymentResult['message']);
            }
        });
    }

    /**
     * Validate order data
     */
    private function validateOrderData(Cart $cart, array $data)
    {
        // Validate address
        $address = Address::where('id', $data['address_id'])
            ->where('user_id', $cart->user_id)
            ->first();

        if (!$address) {
            throw new \Exception('Invalid address selected');
        }

        // For services with home booking, check if address is in provider's city
        if ($cart->hasServices() && $data['booking_type'] === 'home') {
            $provider = $cart->provider;
            if ($provider && $address->city_id !== $provider->city_id) {
                throw new \Exception('Address must be in the service provider\'s city for home booking');
            }
        }

        // Validate scheduled time for services
        if ($cart->hasServices() && isset($data['scheduled_at'])) {
            $scheduledTime = Carbon::parse($data['scheduled_at']);
            if ($scheduledTime->isPast()) {
                throw new \Exception('Scheduled time cannot be in the past');
            }
        }

        // Check provider availability for services
        if ($cart->hasServices()) {
            $provider = Provider::find($cart->provider_id);
            if (!$provider || !$provider->accept_orders || $provider->status !== 'active') {
                throw new \Exception('Service provider is currently unavailable');
            }
        }
    }

    /**
     * Validate wallet balance
     */
    private function validateWalletBalance(User $user, $totalAmount)
    {
        if ($user->wallet_balance < $totalAmount) {
            return [
                'valid' => false,
                'requires_payment_method_change' => true,
                'message' => 'Wallet balance is insufficient. Please choose another payment method.',
                'wallet_balance' => $user->wallet_balance,
                'required_amount' => $totalAmount,
            ];
        }

        return ['valid' => true];
    }

    /**
     * Check and reserve product quantities
     */
    private function checkAndReserveProductQuantities(Cart $cart)
    {
        foreach ($cart->items as $cartItem) {
            if ($cartItem->isProduct()) {
                $product = $cartItem->item;

                if ($product->quantity < $cartItem->quantity) {
                    throw new \Exception("Insufficient stock for product: {$product->name}");
                }

                // Reserve quantity (freeze until payment completion)
                $product->decrement('quantity', $cartItem->quantity);
            }
        }
    }

    /**
     * Rollback product quantities
     */
    private function rollbackProductQuantities(Order $order)
    {
        foreach ($order->items as $orderItem) {
            if ($orderItem->isProduct()) {
                $product = $orderItem->item;
                if ($product) {
                    $product->increment('quantity', $orderItem->quantity);
                }
            }
        }
    }

    /**
     * Create order from cart
     */
    private function createOrder(User $user, Cart $cart, array $data)
    {
        $orderData = [
            'order_number' => Order::generateOrderNumber(),
            'user_id' => $user->id,
            'provider_id' => $cart->provider_id,
            'address_id' => $data['address_id'],
            'status' => 'pending_payment',
            'payment_method' => $data['payment_method'],
            'payment_status' => 'pending',
            'subtotal' => $cart->subtotal,
            'discount_amount' => $cart->discount_amount,
            'coupon_code' => $cart->coupon_code,
            'booking_fee' => $cart->booking_fee,
            'delivery_fee' => $cart->delivery_fee ?? 0,
            'total' => $cart->total,
            'loyalty_points_used' => $cart->loyalty_points_used,
            'booking_type' => $data['booking_type'] ?? null,
            'delivery_type' => $data['delivery_type'] ?? 'normal',
            'scheduled_at' => isset($data['scheduled_at']) ? Carbon::parse($data['scheduled_at']) : null,
        ];

        return Order::create($orderData);
    }

    /**
     * Create order items from cart items
     */
    private function createOrderItems(Order $order, Cart $cart)
    {
        foreach ($cart->items as $cartItem) {
            OrderItem::create([
                'order_id' => $order->id,
                'item_type' => $cartItem->item_type,
                'item_id' => $cartItem->item_id,
                'name' => $cartItem->item->name,
                'quantity' => $cartItem->quantity,
                'price' => $cartItem->price,
                'total' => $cartItem->total,
                'options' => $cartItem->options,
            ]);
        }
    }

    /**
     * Process payment based on method
     */
    private function processPayment(Order $order, array $data)
    {
        switch ($data['payment_method']) {
            case 'wallet':
                return $this->processWalletPayment($order);

            case 'bank_transfer':
                return $this->processBankTransferPayment($order, $data);

            case 'credit_card':
            case 'mada':
            case 'apple_pay':
                return $this->processElectronicPayment($order, $data);

            default:
                return ['success' => false, 'message' => 'Invalid payment method'];
        }
    }

    /**
     * Process wallet payment
     */
    private function processWalletPayment(Order $order)
    {
        $user = $order->user;

        if ($user->wallet_balance >= $order->total) {
            // Deduct from wallet
            $user->decrement('wallet_balance', $order->total);

            // Update order status
            $order->update([
                'payment_status' => 'paid',
                'status' => 'processing',
                'payment_reference' => 'WALLET-' . time(),
            ]);

            // Process loyalty points
            $this->processLoyaltyPoints($order, 'wallet');

            return ['success' => true, 'message' => 'Payment successful'];
        }

        return ['success' => false, 'message' => 'Insufficient wallet balance'];
    }

    /**
     * Process bank transfer payment
     */
    private function processBankTransferPayment(Order $order, array $data)
    {
        // For bank transfer, order stays pending until admin confirms
        $order->update([
            'status' => 'pending_payment',
            'payment_reference' => 'BANK-' . $order->order_number,
        ]);

        return [
            'success' => true,
            'message' => 'Order created successfully. Please complete bank transfer.',
            'requires_bank_transfer' => true,
            'bank_details' => $this->getBankTransferDetails(),
        ];
    }

    /**
     * Process electronic payment (credit card, mada, apple pay)
     */
    private function processElectronicPayment(Order $order, array $data)
    {
        // This would integrate with payment gateway
        // For now, we'll simulate the process

        return [
            'success' => true,
            'message' => 'Redirect to payment gateway',
            'requires_payment_gateway' => true,
            'payment_url' => $this->generatePaymentGatewayUrl($order),
        ];
    }

    /**
     * Process loyalty points after successful payment
     */
    private function processLoyaltyPoints(Order $order, string $paymentMethod)
    {
        $loyaltyResult = $this->loyaltyPointsService->processOrderLoyaltyPoints(
            $order->user,
            $order->total,
            $order->loyalty_points_used,
            $paymentMethod
        );

        $order->update([
            'loyalty_points_earned' => $loyaltyResult['points_earned'],
        ]);
    }

    /**
     * Get bank transfer details
     */
    private function getBankTransferDetails()
    {
        return [
            'bank_name' => 'البنك الأهلي السعودي',
            'beneficiary_name' => 'شركة سوريسو للتقنية',
            'account_number' => '**********',
            'iban' => 'SA********************12',
        ];
    }

    /**
     * Generate payment gateway URL
     */
    private function generatePaymentGatewayUrl(Order $order)
    {
        // This would generate actual payment gateway URL
        return "https://payment-gateway.com/pay/{$order->order_number}";
    }

    /**
     * Confirm payment (called by payment gateway webhook or admin)
     */
    public function confirmPayment(Order $order, string $paymentReference = null)
    {
        return DB::transaction(function () use ($order, $paymentReference) {
            $order->update([
                'payment_status' => 'paid',
                'status' => 'processing',
                'payment_reference' => $paymentReference ?? $order->payment_reference,
            ]);

            // Process loyalty points for electronic payments
            if (in_array($order->payment_method, ['credit_card', 'mada', 'apple_pay'])) {
                $this->processLoyaltyPoints($order, 'electronic');
            }

            return $order;
        });
    }

    /**
     * Cancel order and restore product quantities
     */
    public function cancelOrder(Order $order, string $reason = null)
    {
        return DB::transaction(function () use ($order, $reason) {
            if (!$order->canBeCancelled()) {
                throw new \Exception('Order cannot be cancelled at this stage');
            }

            // Restore product quantities
            $this->rollbackProductQuantities($order);

            // Refund wallet if paid via wallet
            if ($order->payment_method === 'wallet' && $order->payment_status === 'paid') {
                $order->user->increment('wallet_balance', $order->total);
            }

            // Restore loyalty points if used
            if ($order->loyalty_points_used > 0) {
                $order->user->increment('loyalty_points', $order->loyalty_points_used);
            }

            $order->update([
                'status' => 'cancelled',
                'cancellation_reason' => $reason,
            ]);

            return $order;
        });
    }

}







    /**
     * Create a new order from the user's cart
     *
     * @param User $user
     * @param array $data
     * @return Order|array
     * @throws \Exception
     */
    public function createOrder(User $user, array $data)
    {
        $cart = $this->cartService->getCart($user);

        if ($cart->items->isEmpty()) {
            return [
                'status'  => 'error',
                'message' => __('apis.cart_empty'),
            ];
        }

        // Get address if provided and ensure it belongs to the user
        $address = null;
        if (! empty($data['address_id'])) {
            $address = Address::where('user_id', $user->id)->find($data['address_id']);
            if (! $address) {
                throw new \Exception(__('apis.address_not_found'));
            }
        }

        // Start transaction
        DB::beginTransaction();

        try {
            // Create the order record
            $orderResult = $this->createOrderRecord($user, $cart, $data, $address);

            // Check if there was an error with coupon validation
            if (is_array($orderResult) && isset($orderResult['status']) && $orderResult['status'] === 'error') {
                DB::rollBack();
                return $orderResult;
            }

            // If no error, $orderResult is the Order object
            $order = $orderResult;

            // Process cart items and create order items
            $this->processCartItems($order, $cart);

            // Add initial order status
            $this->addOrderStatusHistory($order, $user, OrderStatus::NEW);

            // Clear the cart after successful order creation
            $this->clearCart($cart);

            // Notify all active delivery personnel about the new order
            $this->notifyDeliveryPersonnel($order);

            DB::commit();

            return $order->load('items');
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('Order creation failed', ['error' => $e->getMessage(), 'user_id' => $user->id]);
            throw new \Exception(__('apis.order_creation_failed'));
        }
    }

    /**
     * Create the order record in the database
     *
     * @param User $user
     * @param Cart $cart
     * @param array $data
     * @param Address|null $address
     * @return Order|array
     */
    private function createOrderRecord(User $user, Cart $cart, array $data, ?Address $address): Order | array
    {
        // Get VAT percentage from settings
        $settings      = SettingService::appInformations(\App\Models\SiteSetting::pluck('value', 'key'));
        $vatPercentage = (float) ($settings['vat_amount'] ?? 15);

        // Calculate VAT amount based on the setting
        $vatAmount = $cart->total_products * ($vatPercentage / 100);

        // Initialize coupon variables
        $couponAmount = 0;
        $couponId     = null;
        $couponType   = null;
        $couponValue  = null;

        // Check if coupon is provided
        if (! empty($data['coupon_num'])) {
            $couponResult = $this->applyCoupon($data['coupon_num'], $cart->total_products);

            if ($couponResult['status'] === 'success') {
                $couponAmount = $couponResult['discount_amount'];
                $couponId     = $couponResult['coupon_id'];
                $couponType   = $couponResult['coupon_type'];
                $couponValue  = $couponResult['coupon_value'];
            } else {
                // If coupon validation fails, return error data
                return [
                    'status'  => 'error',
                    'message' => $couponResult['message'],
                ];
            }
        }

        // Calculate final total with VAT and coupon discount
        $finalTotal = $cart->total_products + $vatAmount - $couponAmount;

        // Ensure final total is not negative
        if ($finalTotal < 0) {
            $finalTotal = 0;
        }

        // Prepare order data
        $orderData = [
            'user_id'            => $user->id,
            'total_products'     => $cart->total_products,
            'vat_amount'         => $vatAmount,
            'coupon_id'          => $couponId,
            'coupon_num'         => isset($data['coupon_num']) ? $data['coupon_num'] : null,
            'coupon_type'        => $couponType,
            'coupon_value'       => $couponValue,
            'coupon_amount'      => $couponAmount,
            'final_total'        => $finalTotal,
            'address_id'         => $data['address_id'],
            'payment_method_id'  => isset($data['payment_method_id']) ? $data['payment_method_id'] : null,
            'delivery_period_id' => isset($data['delivery_period_id']) ? $data['delivery_period_id'] : null,
            'city_id'            => $address ? $address->city_id : (isset($data['city_id']) ? $data['city_id'] : null),
            'lat'                => $address ? $address->latitude : (isset($data['lat']) ? $data['lat'] : null),
            'lng'                => $address ? $address->longitude : (isset($data['lng']) ? $data['lng'] : null),
            'map_desc'           => isset($data['map_desc']) ? $data['map_desc'] : null,
            'notes'              => isset($data['notes']) ? $data['notes'] : null,
        ];

        return Order::create($orderData);
    }

    /**
     * Process cart items, create order items and update product stock
     *
     * @param Order $order
     * @param Cart $cart
     * @throws \Exception
     */
    private function processCartItems(Order $order, Cart $cart): void
    {
        foreach ($cart->items as $cartItem) {
            // Process each cart item
            $this->processCartItem($order, $cartItem);
        }
    }

    /**
     * Process a single cart item
     *
     * @param Order $order
     * @param CartItem $cartItem
     * @throws \Exception
     */
    private function processCartItem(Order $order, $cartItem): void
    {
        // Lock the product row to prevent race conditions
        $product = Product::where('id', $cartItem->product_id)->lockForUpdate()->first();

        if (! $product) {
            throw new \Exception(__('apis.product_not_found'));
        }

        // Check stock availability
        if ($product->stock < $cartItem->quantity) {
            throw new \Exception(__('apis.product_out_of_stock', ['product' => $product->title]));
        }

        // Create order item
        $this->createOrderItem($order, $product, $cartItem);

        // Update product stock
        $this->updateProductStock($product, $cartItem->quantity);
    }

    /**
     * Create an order item
     *
     * @param Order $order
     * @param Product $product
     * @param CartItem $cartItem
     * @return void
     */
    private function createOrderItem(Order $order, Product $product, $cartItem): void
    {
        $order->items()->create([
            'product_id'     => $product->id,
            'quantity'       => $cartItem->quantity,
            'price'          => $cartItem->price,
            'discount_price' => $cartItem->discount_price,
            'total'          => $cartItem->total,
        ]);
    }

    /**
     * Update product stock
     *
     * @param Product $product
     * @param int $quantity
     * @return void
     */
    private function updateProductStock(Product $product, int $quantity): void
    {
        $product->update([
            'stock' => $product->stock - $quantity,
        ]);
    }

    /**
     * Add order status history
     *
     * @param Order $order
     * @param User $user
     * @param string $status
     * @return void
     */
    private function addOrderStatusHistory(Order $order, User $user, string $status): void
    {
        $order->statusHistory()->create([
            'status'          => $status,
            'statusable_id'   => $user->id,
            'statusable_type' => get_class($user),
        ]);
    }

    /**
     * Cancel an order
     *
     * @param User $user
     * @param int $orderId
     * @param string|null $reason
     * @return Order
     * @throws \Exception
     */
    public function cancelOrder(User $user, int $orderId, ?string $reason = null)
    {
        $order = $user->orders()->findOrFail($orderId);

        // Check if order can be canceled
        $this->validateOrderCanBeCanceled($order);

        // Start transaction
        DB::beginTransaction();

        try {
            // Update order status to canceled
            $this->updateOrderToCanceled($order, $reason);

            // Add order status history
            $this->addOrderStatusHistory($order, $user, OrderStatus::CANCELLED);

            // Return products to stock
            $this->returnProductsToStock($order);

            // Process refund if payment was made
            // This would be implemented based on your payment gateway integration

            DB::commit();

            return $order->load('items');
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Validate that an order can be canceled
     *
     * @param Order $order
     * @throws \Exception
     */
    private function validateOrderCanBeCanceled(Order $order): void
    {
        if ($order->status != OrderStatus::NEW) {
            throw new \Exception(__('apis.order_cannot_be_canceled'));
        }
    }

    /**
     * Update order status to canceled
     *
     * @param Order $order
     * @param string|null $reason
     * @return void
     */
    private function updateOrderToCanceled(Order $order, ?string $reason): void
    {
        $order->update([
            'status' => OrderStatus::CANCELLED,
            'notes'  => $reason,
        ]);
    }

    /**
     * Return products to stock when an order is canceled
     *
     * @param Order $order
     * @return void
     */
    private function returnProductsToStock(Order $order): void
    {
        foreach ($order->items as $item) {
            $product = $item->product;
            if ($product) {
                $this->increaseProductStock($product, $item->quantity);
            }
        }
    }

    /**
     * Increase product stock
     *
     * @param Product $product
     * @param int $quantity
     * @return void
     */
    private function increaseProductStock(Product $product, int $quantity): void
    {
        $product->update([
            'stock' => $product->stock + $quantity,
        ]);
    }

    /**
     * Finish an order
     *
     * @param int $orderId
     * @return Order
     */


    /**
     * Update order status to finished
     *
     * @param Order $order
     * @return void
     */
    private function updateOrderToFinished(Order $order): void
    {
        $order->update([
            'status' => OrderStatus::DELIVERED,
        ]);
    }

    /**
     * Send notification to user about order status
     *
     * @param Order $order
     * @return void
     */
    private function sendOrderNotification(Order $order): void
    {
        $order->user->notify(new OrderNotification($order, $order->user));
    }

    /**
     * Notify all active delivery personnel about a new order
     *
     * @param Order $order
     * @return void
     */
    private function notifyDeliveryPersonnel(Order $order): void
    {
        // Get all active delivery personnel
        $deliveryPersonnel = User::where('type', 'delivery')
            ->where('active', true)
            ->where('is_blocked', false)
            ->get();

        if ($deliveryPersonnel->isNotEmpty()) {
            // Send notification to all active delivery personnel
            Notification::send($deliveryPersonnel, new NewOrderNotification($order));
        }
    }

    /**
     * Update order status (except cancel)
     *
     * @param User $user
     * @param int $orderId
     * @param string $status
     * @return Order
     * @throws \Exception
     */
    public function updateOrderStatus(User $user, int $orderId, string $status)
    {
        $order = Order::findOrFail($orderId);

        // Validate the status is not CANCEL
        $this->validateStatusNotCancel($status);

        // Start transaction
        DB::beginTransaction();

        try {
            // Update the order status
            $this->setOrderStatus($order, $status);

            // Add order status history
            $this->addOrderStatusHistory($order, $user, $status);

            // Send notification to the order's user about status change
            $order->user->notify(new OrderStatusUpdateNotification($order));

            DB::commit();

            return $order->load('items');
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Validate that the status is not CANCEL
     *
     * @param string $status
     * @throws \Exception
     */
    private function validateStatusNotCancel(string $status): void
    {
        if ($status == OrderStatus::CANCELLED) {
            throw new \Exception(__('apis.cannot_use_cancel_status'));
        }
    }

    /**
     * Set the order status
     *
     * @param Order $order
     * @param string $status
     * @return void
     */
    private function setOrderStatus(Order $order, string $status): void
    {
        $order->update([
            'status' => $status,
        ]);
    }



    /**
     * Clear the cart
     *
     * @param \App\Models\Cart $cart
     * @return void
     */
    private function clearCart($cart)
    {
        $cart->items()->delete();
        $cart->update([
            'total_qty'      => 0,
            'total_products' => 0,
            'final_total'    => 0,
        ]);
    }

    /**
     * Apply coupon to the order
     *
     * @param string $couponNum
     * @param float $totalPrice
     * @return array
     */
    private function applyCoupon(string $couponNum, float $totalPrice): array
    {
        // Find the coupon
        $coupon = Coupon::where('coupon_num', $couponNum)->first();

        if (! $coupon) {
            return [
                'status'  => 'error',
                'message' => __('apis.not_avilable_coupon'),
            ];
        }

        // Check if coupon is available
        if ($coupon->status == 'closed') {
            return [
                'status'  => 'error',
                'message' => __('apis.not_avilable_coupon'),
            ];
        }

        // Check if coupon usage limit is reached
        if ($coupon->status == 'usage_end' || $coupon->use_times >= $coupon->max_use) {
            return [
                'status'  => 'error',
                'message' => __('apis.max_usa_coupon'),
            ];
        }

        // Check if coupon is expired
        if ($coupon->expire_date < now() || $coupon->status == 'expire') {
            return [
                'status'  => 'error',
                'message' => __('apis.coupon_end_at', ['date' => $coupon->expire_date ? date('d-m-Y h:i A', strtotime($coupon->expire_date)) : '']),
            ];
        }

        // Check if coupon start date is in the future
        if ($coupon->start_date > now()) {
            return [
                'status'  => 'error',
                'message' => __('apis.coupon_start_at', ['date' => $coupon->start_date ? date('d-m-Y h:i A', strtotime($coupon->start_date)) : '']),
            ];
        }

        // Calculate discount amount
        $discountAmount = 0;

        if ($coupon->type == 'ratio') {
            // Percentage discount
            $discountAmount = ($coupon->discount * $totalPrice) / 100;

            // Check if discount exceeds max discount
            if ($discountAmount > $coupon->max_discount) {
                $discountAmount = $coupon->max_discount;
            }
        } else {
            // Fixed amount discount
            $discountAmount = $coupon->discount;

            // Ensure discount doesn't exceed total price
            if ($discountAmount > $totalPrice) {
                $discountAmount = $totalPrice;
            }
        }

        // Increment coupon usage
        $coupon->increment('use_times');

        // Check if max usage reached after increment
        if ($coupon->use_times >= $coupon->max_use) {
            $coupon->update(['status' => 'usage_end']);
        }

        return [
            'status'          => 'success',
            'message'         => __('apis.disc_amount') . ' ' . $discountAmount,
            'discount_amount' => $discountAmount,
            'coupon_id'       => $coupon->id,
            'coupon_type'     => $coupon->type,
            'coupon_value'    => $coupon->discount,
        ];
    }
}
