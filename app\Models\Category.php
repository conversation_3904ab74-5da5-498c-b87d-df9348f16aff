<?php

namespace App\Models;

use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;

class Category extends BaseModel implements HasMedia
{
    use HasTranslations , InteractsWithMedia;


    protected $fillable = ['name','is_active'];
    public $translatable = ['name'];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('categories')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
            ->useFallbackUrl(asset('storage/images/categories/default.png'))
            ->useFallbackPath(public_path('storage/images/categories/default.png'));
    }

    /**
     * Register media conversions for categories
     */
    public function registerMediaConversions($media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->nonQueued();

        $this->addMediaConversion('medium')
            ->width(500)
            ->height(500)
            ->nonQueued();
    }

    /**
     * Get category image URL
     */
    public function getImageUrlAttribute()
    {
        return $this->getFirstMediaUrl('categories');
    }

    /**
     * Get category image URL with conversion
     */
    public function getImageUrl($conversion = null)
    {
        if ($conversion) {
            return $this->getFirstMediaUrl('categories', $conversion);
        }
        return $this->getFirstMediaUrl('categories');
    }

    /**
     * Get the services for the category
     */
    public function services()
    {
        return $this->hasMany(\App\Models\Service::class);
    }

    public function getServicesCountAttribute()
    {
        return $this->services()->count();
    }
}
