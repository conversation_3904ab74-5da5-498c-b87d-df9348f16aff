<div class="position-relative">
    
    
    

    
 <table class="table" id="tab">
    <thead>
        <tr>
            <th><?php echo e(__('admin.order_num')); ?></th>
            <th><?php echo e(__('admin.user')); ?></th>
            
            <th><?php echo e(__('admin.status')); ?></th>
            <th><?php echo e(__('admin.final_total')); ?></th>
            <th><?php echo e(__('admin.control')); ?></th>
        </tr>
    </thead>
    <tbody>
        <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($order->order_num); ?></td>
                <td><?php echo e($order->user ? $order->user->name : '-'); ?></td>
                         <td><?php echo e(\App\Enums\OrderStatus::getLabel($order->status)); ?></td>
                
                <td><?php echo e(number_format($order->final_total, 2)); ?></td>
                <td class="order-action">
                  <a href="<?php echo e(route('admin.orders.show', ['id' => $order->id])); ?>" class="btn btn-warning btn-sm">
                            عرض
                        </a>
                    
                    
                </td>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
</table>


    
    <?php if($orders->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>


<?php if($orders->count() > 0 && $orders instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($orders->links()); ?>

    </div>
<?php endif; ?>

<?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/orders/table.blade.php ENDPATH**/ ?>