<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CartItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'cart_id',
        'item_type',
        'item_id',
        'quantity',
        'price',
        'total',
        'options'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'total' => 'decimal:2',
        'quantity' => 'integer',
        'options' => 'array'
    ];

    /**
     * Get the cart that owns the cart item
     */
    public function cart()
    {
        return $this->belongsTo(Cart::class);
    }

    /**
     * Get the owning item model (Product or Service)
     */
    public function item()
    {
        return $this->morphTo();
    }

    /**
     * Get the product if item is a product
     */
    public function product()
    {
        return $this->belongsTo(Product::class, 'item_id')->where('item_type', 'App\Models\Product');
    }

    /**
     * Get the service if item is a service
     */
    public function service()
    {
        return $this->belongsTo(Service::class, 'item_id')->where('item_type', 'App\Models\Service');
    }

    /**
     * Check if item is a product
     */
    public function isProduct()
    {
        return $this->item_type === 'App\Models\Product';
    }

    /**
     * Check if item is a service
     */
    public function isService()
    {
        return $this->item_type === 'App\Models\Service';
    }

    /**
     * Get the provider of the item
     */
    public function getProviderAttribute()
    {
        if ($this->isProduct()) {
            return $this->item->provider;
        } elseif ($this->isService()) {
            return $this->item->provider;
        }
        return null;
    }

    /**
     * Get item name
     */
    public function getItemNameAttribute()
    {
        return $this->item ? $this->item->name : null;
    }

    /**
     * Get item image (for products)
     */
    public function getItemImageAttribute()
    {
        if ($this->isProduct() && $this->item) {
            return $this->item->product_images_urls->first() ?? null;
        }
        return null;
    }
}
