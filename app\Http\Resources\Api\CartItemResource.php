<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class CartItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'item_type' => $this->item_type,
            'item_id' => $this->item_id,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'total' => $this->total,
            'options' => $this->options,

            // Item details
            'item_name' => $this->item_name,
            'item_image' => $this->item_image,
            'provider_name' => $this->provider ? $this->provider->commercial_name : null,

            // Type-specific details
            'is_product' => $this->isProduct(),
            'is_service' => $this->isService(),

            // Product details (if product)
            'product' => $this->when($this->isProduct() && $this->relationLoaded('item'), function() {
                return [
                    'id' => $this->item->id,
                    'name' => $this->item->name,
                    'price' => $this->item->price,
                    'quantity_available' => $this->item->quantity,
                    'is_active' => $this->item->is_active,
                    'provider_id' => $this->item->provider_id,
                    'images' => $this->item->product_images_urls ?? [],
                ];
            }),

            // Service details (if service)
            'service' => $this->when($this->isService() && $this->relationLoaded('item'), function() {
                return [
                    'id' => $this->item->id,
                    'name' => $this->item->name,
                    'price' => $this->item->price,
                    'duration' => $this->item->duration,
                    'formatted_duration' => $this->item->formatted_duration,
                    'is_active' => $this->item->is_active,
                    'provider_id' => $this->item->provider_id,
                    'booking_time' => $this->options['booking_time'] ?? null,
                ];
            }),
        ];
    }
}
