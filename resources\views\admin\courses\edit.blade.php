@extends('admin.layout.master')

@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
    <style>
        .stage-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        .stage-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .remove-stage {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 5px 10px;
            cursor: pointer;
        }
        .video-preview-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .video-preview {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .current-video {
            border: 1px solid #28a745;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8fff9;
        }
    </style>
@endsection

@section('content')
<!-- Basic multiple Column Form section start -->
<form method="POST" action="{{route('admin.courses.update', $course->id)}}" class="store form-horizontal" novalidate enctype="multipart/form-data">
<section id="multiple-column-form">
    <div class="row">
        <div class="col-md-3">
            <div class="col-12 card card-body">
                <div class="imgMontg col-12 text-center">
                    <div class="dropBox">
                        <div class="textCenter">
                            <div class="imagesUploadBlock">
                                <label class="uploadImg">
                                    <span><i class="feather icon-image"></i></span>
                                    <input type="file" accept="image/*" name="image" class="imageUploader">
                                </label>
                                <div class="uploadedBlock">
                                    <img src="{{ $course->image }}">
                                    <button class="close"><i class="la la-times"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-9">
            <div class="card">
                <div class="card-content">
                    <div class="card-body">
                        @csrf
                        @method('PUT')
                        <div class="form-body">
                            <div class="row">
                                <!-- Course Name Arabic -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="name_ar">اسم الدورة (عربي) <span class="text-danger">*</span></label>
                                        <input type="text" id="name_ar" class="form-control" name="name[ar]" value="{{ $course->getTranslation('name', 'ar') }}" required minlength="5">
                                    </div>
                                </div>

                                <!-- Course Name English -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="name_en">اسم الدورة (إنجليزي) <span class="text-danger">*</span></label>
                                        <input type="text" id="name_en" class="form-control" name="name[en]" value="{{ $course->getTranslation('name', 'en') }}" required minlength="5">
                                    </div>
                                </div>

                                <!-- Instructor Name Arabic -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="instructor_name_ar">اسم المدرب (عربي) <span class="text-danger">*</span></label>
                                        <input type="text" id="instructor_name_ar" class="form-control" name="instructor_name[ar]" value="{{ $course->getTranslation('instructor_name', 'ar') }}" required minlength="5">
                                    </div>
                                </div>

                                <!-- Instructor Name English -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="instructor_name_en">اسم المدرب (إنجليزي) <span class="text-danger">*</span></label>
                                        <input type="text" id="instructor_name_en" class="form-control" name="instructor_name[en]" value="{{ $course->getTranslation('instructor_name', 'en') }}" required minlength="5">
                                    </div>
                                </div>

                                <!-- Duration -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="duration">مدة الدورة (بالساعات) <span class="text-danger">*</span></label>
                                        <input type="number" id="duration" class="form-control" name="duration" value="{{ $course->duration }}" required min="1" step="0.5">
                                    </div>
                                </div>

                                <!-- Price -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="price">سعر الدورة <span class="text-danger">*</span></label>
                                        <input type="number" id="price" class="form-control" name="price" value="{{ $course->price }}" required min="0" step="0.01">
                                    </div>
                                </div>

                                <!-- Description Arabic -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="description_ar">وصف الدورة (عربي) <span class="text-danger">*</span></label>
                                        <textarea id="description_ar" class="form-control" name="description[ar]" rows="4" required minlength="20">{{ $course->getTranslation('description', 'ar') }}</textarea>
                                    </div>
                                </div>

                                <!-- Description English -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="description_en">وصف الدورة (إنجليزي) <span class="text-danger">*</span></label>
                                        <textarea id="description_en" class="form-control" name="description[en]" rows="4" required minlength="20">{{ $course->getTranslation('description', 'en') }}</textarea>
                                    </div>
                                </div>

                                <!-- Active Status -->
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1" {{ $course->is_active ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="is_active">مفعل للعملاء</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Stages Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">مراحل الدورة</h4>
                    <button type="button" class="btn btn-primary" id="add-stage">إضافة مرحلة</button>
                </div>
                <div class="card-content">
                    <div class="card-body">
                        <div id="stages-container">
                            <!-- Existing stages will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 d-flex justify-content-end">
            <button type="submit" class="btn btn-primary mr-1 mb-1">تحديث</button>
            <a href="{{ route('admin.courses.index') }}" class="btn btn-light-secondary mr-1 mb-1">إلغاء</a>
        </div>
    </div>
</section>
</form>
@endsection

@section('js')
    <script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
    <script>
        let stageIndex = 0;

        // Add stage functionality
        $('#add-stage').click(function() {
            addStage();
        });

        // Load existing stages
        $(document).ready(function() {
            @foreach($course->stages as $index => $stage)
                loadExistingStage(
                    {{ $index }},
                    '{{ $stage->getTranslation('title', 'ar') }}',
                    '{{ $stage->getTranslation('title', 'en') }}',
                    '{{ $stage->video_name }}',
                    '{{ $stage->video }}'
                );
            @endforeach

            stageIndex = {{ count($course->stages) }};
        });

        function loadExistingStage(index, titleAr, titleEn, videoName, videoUrl) {
            const stageHtml = `
                <div class="stage-item" data-index="${index}">
                    <div class="stage-header">
                        <h5>المرحلة ${index + 1}</h5>
                        <button type="button" class="remove-stage" onclick="removeStage(this)">حذف المرحلة</button>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <div class="form-group">
                                <label>عنوان المرحلة (عربي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="stages[${index}][title][ar]" value="${titleAr}" required minlength="5">
                            </div>
                        </div>
                        <div class="col-md-6 col-12">
                            <div class="form-group">
                                <label>عنوان المرحلة (إنجليزي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="stages[${index}][title][en]" value="${titleEn}" required minlength="5">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label>فيديو المرحلة</label>
                                <input type="file" class="form-control video-input" name="stages[${index}][video]" accept="video/*" onchange="previewVideo(this)">
                                <small class="text-muted">الحد الأقصى لحجم الملف: 50 ميجابايت</small>
                                ${videoUrl ? `
                                    <div class="current-video mt-3" id="current-video-${index}">
                                        <div class="video-preview-container">
                                            <video width="300" height="200" controls style="border-radius: 5px;">
                                                <source src="${videoUrl}" type="video/mp4">
                                                متصفحك لا يدعم عرض الفيديو
                                            </video>
                                            <div class="mt-2">
                                                <strong>الفيديو الحالي:</strong> ${videoName}
                                                <button type="button" class="btn btn-sm btn-danger ml-2" onclick="removeCurrentVideo(${index})">
                                                    <i class="feather icon-trash"></i> حذف الفيديو
                                                </button>
                                            </div>
                                            <input type="hidden" name="stages[${index}][remove_video]" value="0" id="remove-video-${index}">
                                        </div>
                                    </div>
                                ` : ''}
                                <div class="video-preview mt-3" id="video-preview-${index}" style="display: none;">
                                    <video width="300" height="200" controls style="border-radius: 5px;">
                                        متصفحك لا يدعم عرض الفيديو
                                    </video>
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeVideoPreview(${index})">
                                            <i class="feather icon-trash"></i> إزالة الفيديو الجديد
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#stages-container').append(stageHtml);
        }

        function addStage() {
            const stageHtml = `
                <div class="stage-item" data-index="${stageIndex}">
                    <div class="stage-header">
                        <h5>المرحلة ${stageIndex + 1}</h5>
                        <button type="button" class="remove-stage" onclick="removeStage(this)">حذف المرحلة</button>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <div class="form-group">
                                <label>عنوان المرحلة (عربي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="stages[${stageIndex}][title][ar]" required minlength="5">
                            </div>
                        </div>
                        <div class="col-md-6 col-12">
                            <div class="form-group">
                                <label>عنوان المرحلة (إنجليزي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="stages[${stageIndex}][title][en]" required minlength="5">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label>فيديو المرحلة <span class="text-danger">*</span></label>
                                <input type="file" class="form-control video-input" name="stages[${stageIndex}][video]" accept="video/*" required onchange="previewVideo(this)">
                                <small class="text-muted">الحد الأقصى لحجم الملف: 50 ميجابايت</small>
                                <div class="video-preview mt-3" id="video-preview-${stageIndex}" style="display: none;">
                                    <video width="300" height="200" controls style="border-radius: 5px;">
                                        متصفحك لا يدعم عرض الفيديو
                                    </video>
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeVideoPreview(${stageIndex})">
                                            <i class="feather icon-trash"></i> إزالة الفيديو
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#stages-container').append(stageHtml);
            stageIndex++;
            updateStageNumbers();
        }

        function removeStage(button) {
            if ($('.stage-item').length > 1) {
                $(button).closest('.stage-item').remove();
                updateStageNumbers();
            } else {
                Swal.fire('تنبيه!', 'يجب أن تحتوي الدورة على مرحلة واحدة على الأقل', 'warning');
            }
        }

        function updateStageNumbers() {
            $('.stage-item').each(function(index) {
                $(this).find('.stage-header h5').text('المرحلة ' + (index + 1));
            });
        }

        // Video preview functionality
        function previewVideo(input) {
            const stageIndex = $(input).closest('.stage-item').data('index');
            const previewContainer = $('#video-preview-' + stageIndex);
            const video = previewContainer.find('video')[0];

            if (input.files && input.files[0]) {
                const file = input.files[0];
                const url = URL.createObjectURL(file);

                video.src = url;
                previewContainer.show();

                // Clean up the object URL when the video is loaded
                video.onload = function() {
                    URL.revokeObjectURL(url);
                };
            }
        }

        // Remove video preview for new videos
        function removeVideoPreview(stageIndex) {
            const previewContainer = $('#video-preview-' + stageIndex);
            const video = previewContainer.find('video')[0];
            const input = $(`.stage-item[data-index="${stageIndex}"] .video-input`)[0];

            video.src = '';
            input.value = '';
            previewContainer.hide();
        }

        // Remove current video (existing videos)
        function removeCurrentVideo(stageIndex) {
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: 'سيتم حذف الفيديو الحالي نهائياً',
                type: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.value) {
                    $('#current-video-' + stageIndex).hide();
                    $('#remove-video-' + stageIndex).val('1');

                    Swal.fire(
                        'تم الحذف!',
                        'سيتم حذف الفيديو عند حفظ التغييرات',
                        'success'
                    );
                }
            });
        }
    </script>

    
@include('admin.shared.addImage')
{{-- show selected image script --}}

{{-- submit add form script --}}
@include('admin.shared.submitAddForm')

@endsection