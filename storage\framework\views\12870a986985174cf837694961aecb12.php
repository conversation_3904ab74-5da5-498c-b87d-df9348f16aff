<?php $__env->startSection('css'); ?>
    <style>
        .course-info-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        }
        .stage-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #fff;
        }
        .course-image {
            max-width: 300px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .info-row {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        .status-badge {
            font-size: 14px;
            padding: 8px 16px;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title">تفاصيل الدورة التدريبية</h4>
                <div>
                    <a href="<?php echo e(route('admin.courses.edit', $course->id)); ?>" class="btn btn-primary">
                        <i class="la la-edit"></i> تعديل
                    </a>
                    <a href="<?php echo e(route('admin.courses.index')); ?>" class="btn btn-secondary">
                        <i class="la la-arrow-left"></i> العودة
                    </a>
                </div>
            </div>
            <div class="card-content">
                <div class="card-body">
                    <div class="row">
                        <!-- Course Image -->
                        <div class="col-md-4 text-center">
                            <img src="<?php echo e($course->image); ?>" alt="Course Image" class="course-image img-fluid">
                        </div>
                        
                        <!-- Course Information -->
                        <div class="col-md-8">
                            <div class="course-info-card">
                                <div class="row info-row">
                                    <div class="col-md-3 info-label">اسم الدورة (عربي):</div>
                                    <div class="col-md-9"><?php echo e($course->getTranslation('name', 'ar')); ?></div>
                                </div>
                                
                                <div class="row info-row">
                                    <div class="col-md-3 info-label">اسم الدورة (إنجليزي):</div>
                                    <div class="col-md-9"><?php echo e($course->getTranslation('name', 'en')); ?></div>
                                </div>
                                
                                <div class="row info-row">
                                    <div class="col-md-3 info-label">اسم المدرب (عربي):</div>
                                    <div class="col-md-9"><?php echo e($course->getTranslation('instructor_name', 'ar')); ?></div>
                                </div>
                                
                                <div class="row info-row">
                                    <div class="col-md-3 info-label">اسم المدرب (إنجليزي):</div>
                                    <div class="col-md-9"><?php echo e($course->getTranslation('instructor_name', 'en')); ?></div>
                                </div>
                                
                                <div class="row info-row">
                                    <div class="col-md-3 info-label">مدة الدورة:</div>
                                    <div class="col-md-9"><?php echo e($course->duration); ?> ساعة</div>
                                </div>
                                
                                <div class="row info-row">
                                    <div class="col-md-3 info-label">سعر الدورة:</div>
                                    <div class="col-md-9"><?php echo e(number_format($course->price, 2)); ?> ريال</div>
                                </div>
                                
                                <div class="row info-row">
                                    <div class="col-md-3 info-label">عدد المراحل:</div>
                                    <div class="col-md-9">
                                        <span class="badge badge-info"><?php echo e($course->stages_count); ?> مرحلة</span>
                                    </div>
                                </div>
                                
                                <div class="row info-row">
                                    <div class="col-md-3 info-label">الحالة:</div>
                                    <div class="col-md-9">
                                        <?php if($course->is_active): ?>
                                            <span class="badge badge-success status-badge">مفعل</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger status-badge">غير مفعل</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="row info-row">
                                    <div class="col-md-3 info-label">تاريخ الإنشاء:</div>
                                    <div class="col-md-9"><?php echo e($course->created_at->format('d/m/Y H:i')); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Course Description -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>وصف الدورة</h5>
                            <div class="course-info-card">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>الوصف باللغة العربية:</h6>
                                        <p><?php echo e($course->getTranslation('description', 'ar')); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>الوصف باللغة الإنجليزية:</h6>
                                        <p><?php echo e($course->getTranslation('description', 'en')); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Course Stages -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>مراحل الدورة</h5>
                            <?php if($course->stages->count() > 0): ?>
                                <?php $__currentLoopData = $course->stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="stage-card">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">المرحلة <?php echo e($index + 1); ?></h6>
                                            <span class="badge badge-primary">المرحلة <?php echo e($stage->order); ?></span>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="info-row">
                                                    <span class="info-label">العنوان (عربي):</span>
                                                    <?php echo e($stage->getTranslation('title', 'ar')); ?>

                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="info-row">
                                                    <span class="info-label">العنوان (إنجليزي):</span>
                                                    <?php echo e($stage->getTranslation('title', 'en')); ?>

                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php if($stage->video): ?>
                                            <div class="row mt-2">
                                                <div class="col-12">
                                                    <div class="info-row">
                                                        <span class="info-label">الفيديو:</span>
                                                        <a href="<?php echo e($stage->video); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="la la-play"></i> مشاهدة الفيديو
                                                        </a>
                                                        <?php if($stage->video_name): ?>
                                                            <span class="ml-2 text-muted"><?php echo e($stage->video_name); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="la la-info-circle"></i> لا توجد مراحل لهذه الدورة حتى الآن.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/courses/show.blade.php ENDPATH**/ ?>