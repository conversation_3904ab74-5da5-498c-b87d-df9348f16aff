<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
    $table->id();
    $table->string('order_number')->unique();
    $table->foreignId('address_id')->constrained()->onDelete('cascade');

    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->unsignedBigInteger('provider_id')->nullable();
    $table->enum('status', [
        'pending_payment',
        'processing',
        'in_progress',
        'completed',
        'cancelled',
        'refunded'
    ])->default('pending_payment');
    $table->enum('payment_method', [
        'credit_card',
        'wallet',
        'bank_transfer',
        'apple_pay',
        'mada'
    ]);
    $table->enum('payment_status', [
        'pending',
        'paid',
        'failed',
        'refunded'
    ])->default('pending');
    $table->decimal('subtotal', 12, 2);
    $table->decimal('discount_amount', 12, 2)->default(0);
    $table->string('coupon_code')->nullable();
    $table->decimal('booking_fee', 12, 2)->default(0);
    $table->decimal('delivery_fee', 12, 2)->default(0);
    $table->decimal('platform_commission', 12, 2)->default(0);
    $table->decimal('provider_share', 12, 2)->default(0);
    $table->decimal('total', 12, 2);
    $table->integer('loyalty_points_earned')->default(0);
    $table->integer('loyalty_points_used')->default(0);
    $table->text('cancellation_reason')->nullable();
    $table->timestamp('scheduled_at')->nullable();
    $table->string('invoice_number')->nullable();
    $table->string('payment_reference')->nullable();
    $table->timestamps();
    $table->softDeletes();
    
    $table->foreign('provider_id')->references('id')->on('providers');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
