<?php

namespace App\Http\Requests\Admin\Course;

use Illuminate\Foundation\Http\FormRequest;

class Store extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name.ar' => 'required|string|min:5|max:255',
            'name.en' => 'required|string|min:5|max:255',
            'instructor_name.ar' => 'required|string|min:5|max:255',
            'instructor_name.en' => 'required|string|min:5|max:255',
            'duration' => 'required|numeric|min:1',
            'description.ar' => 'required|string|min:20',
            'description.en' => 'required|string|min:20',
            'price' => 'required|numeric|min:0',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            
            // Course stages validation
            'stages' => 'required|array|min:1',
            'stages.*.title.ar' => 'required|string|min:5|max:255',
            'stages.*.title.en' => 'required|string|min:5|max:255',
            'stages.*.video' => 'required|file|mimes:mp4,avi,mov,wmv|max:51200', // 50MB max
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.ar.required' => 'اسم الدورة باللغة العربية مطلوب',
            'name.ar.min' => 'اسم الدورة باللغة العربية يجب أن يكون 5 أحرف على الأقل',
            'name.en.required' => 'اسم الدورة باللغة الإنجليزية مطلوب',
            'name.en.min' => 'اسم الدورة باللغة الإنجليزية يجب أن يكون 5 أحرف على الأقل',
            'instructor_name.ar.required' => 'اسم مقدم الدورة باللغة العربية مطلوب',
            'instructor_name.ar.min' => 'اسم مقدم الدورة باللغة العربية يجب أن يكون 5 أحرف على الأقل',
            'instructor_name.en.required' => 'اسم مقدم الدورة باللغة الإنجليزية مطلوب',
            'instructor_name.en.min' => 'اسم مقدم الدورة باللغة الإنجليزية يجب أن يكون 5 أحرف على الأقل',
            'duration.required' => 'مدة الدورة مطلوبة',
            'duration.min' => 'مدة الدورة يجب أن تكون ساعة واحدة على الأقل',
            'description.ar.required' => 'وصف الدورة باللغة العربية مطلوب',
            'description.ar.min' => 'وصف الدورة باللغة العربية يجب أن يكون 20 حرف على الأقل',
            'description.en.required' => 'وصف الدورة باللغة الإنجليزية مطلوب',
            'description.en.min' => 'وصف الدورة باللغة الإنجليزية يجب أن يكون 20 حرف على الأقل',
            'price.required' => 'سعر الدورة مطلوب',
            'image.required' => 'صورة الدورة مطلوبة',
            'stages.required' => 'مراحل الدورة مطلوبة',
            'stages.min' => 'يجب إضافة مرحلة واحدة على الأقل',
            'stages.*.title.ar.required' => 'عنوان المرحلة باللغة العربية مطلوب',
            'stages.*.title.ar.min' => 'عنوان المرحلة باللغة العربية يجب أن يكون 5 أحرف على الأقل',
            'stages.*.title.en.required' => 'عنوان المرحلة باللغة الإنجليزية مطلوب',
            'stages.*.title.en.min' => 'عنوان المرحلة باللغة الإنجليزية يجب أن يكون 5 أحرف على الأقل',
            'stages.*.video.required' => 'فيديو المرحلة مطلوب',
        ];
    }
}
