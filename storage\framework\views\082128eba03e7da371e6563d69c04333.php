<div class="position-relative">
    
    
    

    
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th><?php echo e(__('admin.date')); ?></th>
                <th><?php echo e(__('admin.orders_count')); ?></th>
                <th><?php echo e(__('admin.month')); ?></th>
                <th><?php echo e(__('admin.coupon')); ?></th>
                <th><?php echo e(__('admin.status')); ?></th>
                <th><?php echo e(__('admin.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $gifts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gift): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                        <input type="checkbox" class="checkSingle" id="<?php echo e($gift->id); ?>">
                        <span class="checkmark"></span>
                        </label>
                    </td>
                    <td><?php echo e($gift->created_at->format('d/m/Y')); ?></td>
                    <td><?php echo e($gift->orders_count); ?></td>
                    <td><?php echo e($gift->month->format('Y-m')); ?></td>
                    <td><?php echo e($gift->coupon ? $gift->coupon->coupon_num : '-'); ?></td>
                    <td>
                        <?php if($gift->is_active): ?>
                        <span class="btn btn-sm round btn-outline-success">
                            <?php echo e(__('admin.active')); ?> <i class="la la-check font-medium-2"></i>
                        </span>
                        <?php else: ?>
                        <span class="btn btn-sm round btn-outline-danger">
                            <?php echo e(__('admin.inactive')); ?> <i class="la la-close font-medium-2"></i>
                        </span>
                        <?php endif; ?>
                    </td>

                    <td class="product-action">
                        <span class="text-primary"><a href="<?php echo e(route('admin.gifts.show', ['id' => $gift->id])); ?>" class="btn btn-warning btn-sm"><i class="feather icon-eye"></i> <?php echo e(__('admin.show')); ?></a></span>
                        <span class="action-edit text-primary"><a href="<?php echo e(route('admin.gifts.edit', ['id' => $gift->id])); ?>" class="btn btn-primary btn-sm"><i class="feather icon-edit"></i><?php echo e(__('admin.edit')); ?></a></span>
                        <span class="delete-row btn btn-danger btn-sm" data-url="<?php echo e(url('admin/gifts/' . $gift->id)); ?>"><i class="feather icon-trash"></i><?php echo e(__('admin.delete')); ?></span>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    
    <?php if($gifts->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>

<?php if($gifts->count() > 0 && $gifts instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($gifts->links()); ?>

    </div>
<?php endif; ?>


<?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/gifts/table.blade.php ENDPATH**/ ?>