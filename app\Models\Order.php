<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'order_number',
        'user_id',
        'provider_id',
        'address_id',
        'status',
        'payment_method',
        'payment_status',
        'subtotal',
        'discount_amount',
        'coupon_code',
        'booking_fee',
        'delivery_fee',
        'platform_commission',
        'provider_share',
        'total',
        'loyalty_points_earned',
        'loyalty_points_used',
        'cancellation_reason',
        'scheduled_at',
        'invoice_number',
        'payment_reference',
        'booking_type',
        'delivery_type',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'booking_fee' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
        'platform_commission' => 'decimal:2',
        'provider_share' => 'decimal:2',
        'total' => 'decimal:2',
        'loyalty_points_earned' => 'integer',
        'loyalty_points_used' => 'integer',
        'scheduled_at' => 'datetime',
    ];

    /**
     * Get the user that owns the order
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the provider associated with the order
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the address for the order
     */
    public function address()
    {
        return $this->belongsTo(Address::class);
    }

    /**
     * Get the order items
     */
    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the coupon applied to the order
     */
    public function coupon()
    {
        return $this->belongsTo(Coupon::class, 'coupon_code', 'coupon_num');
    }

    /**
     * Check if order has services
     */
    public function hasServices()
    {
        return $this->items()->where('item_type', 'App\Models\Service')->exists();
    }

    /**
     * Check if order has products
     */
    public function hasProducts()
    {
        return $this->items()->where('item_type', 'App\Models\Product')->exists();
    }

    /**
     * Generate unique order number
     */
    public static function generateOrderNumber()
    {
        do {
            $orderNumber = 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
        } while (self::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Check if order can be cancelled
     */
    public function canBeCancelled()
    {
        return in_array($this->status, ['pending_payment', 'processing']);
    }

    /**
     * Check if order requires provider availability
     */
    public function requiresProviderAvailability()
    {
        return $this->hasServices() && $this->booking_type === 'home';
    }
}
