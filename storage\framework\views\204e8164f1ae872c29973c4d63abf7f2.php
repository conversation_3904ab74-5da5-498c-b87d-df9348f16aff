<style>
    .switch {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 18px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 10px;
        width: 10px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: rgb(100, 189, 99);
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #2196F3;
    }

    input:checked + .slider:before {
        -webkit-transform: translateX(26px);
        -ms-transform: translateX(26px);
        transform: translateX(26px);
    }

    /* Rounded sliders */
    .slider.round {
        border-radius: 34px;
    }

    .slider.round:before {
        border-radius: 50%;
    }
</style>


<label class="switch">
    <input

            type="checkbox"
            onchange="toggleBoolean(this ,'<?php echo e($url); ?>')"


            <?php echo e($model->$switch == $open ? 'checked' : ''); ?>

    />

    <span class="slider round"></span>
</label>



<?php /**PATH D:\Workstation\sorriso-backend\resources\views/components/admin/toggle-boolean-view.blade.php ENDPATH**/ ?>