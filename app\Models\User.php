<?php

namespace App\Models;

use App\Http\Resources\Api\UserResource;
use App\Traits\SmsTrait;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Jobs\SendEmailJob;
use App\Jobs\SendSms;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @property mixed country_code
 * @property mixed phone
 */
class User extends Authenticatable implements HasMedia
{
    use Notifiable, HasApiTokens, SmsTrait, SoftDeletes, HasFactory, InteractsWithMedia;

    protected $hidden = [
        'password',
    ];

    protected $casts = [
        'is_notify'   => 'boolean',
    ];

    protected $fillable = [
        'name',
        'country_code',
        'phone',
        'email',
        'password',
        'status',
        'is_notify',
        'code',
        'code_expire',
        'wallet_balance',
        'loyalty_points',
        'city_id',
        'region_id',
        'type',
        'gender'
    ];

    // Define media collections
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('profile')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/users/default.png'))
            ->useFallbackPath(public_path('storage/images/users/default.png'));

        $this->addMediaCollection('id_image')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/users/default.png'))
            ->useFallbackPath(public_path('storage/images/users/default.png'));

        $this->addMediaCollection('license_image')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/users/default.png'))
            ->useFallbackPath(public_path('storage/images/users/default.png'));
    }

    // Define media conversions
    public function registerMediaConversions($media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(100)
            ->height(100)
            ->nonQueued(); // Process immediately
    }

    public function scopeSearch($query, $searchArray = [])
    {
        $query->where(function ($query) use ($searchArray) {
            if ($searchArray) {
                foreach ($searchArray as $key => $value) {
                    if (str_contains($key, '_id')) {
                        if (null != $value) {
                            $query->Where($key, $value);
                        }
                    } elseif ('order' == $key) {
                    } elseif ('created_at_min' == $key) {
                        if (null != $value) {
                            $query->WhereDate('created_at', '>=', $value);
                        }
                    } elseif ('created_at_max' == $key) {
                        if (null != $value) {
                            $query->WhereDate('created_at', '<=', $value);
                        }
                    } else {
                        if (null != $value) {
                            $query->Where($key, 'like', '%' . $value . '%');
                        }
                    }
                }
            }
        });
        return $query->orderBy('created_at', request()->searchArray && request()->searchArray['order'] ? request()->searchArray['order'] : 'DESC');
    }

    public function setPhoneAttribute($value)
    {
        if (!empty($value)) {
            $this->attributes['phone'] = fixPhone($value);
        }
    }

    public function setCountryCodeAttribute($value)
    {
        if (!empty($value)) {
            $this->attributes['country_code'] = fixPhone($value);
        }
    }

    public function getFullPhoneAttribute()
    {
        return $this->attributes['country_code'] . $this->attributes['phone'];
    }

    public function getImageAttribute()
    {
        return $this->getFirstMediaUrl('profile') ?: asset('storage/images/users/default.png');
    }

    public function getImageUrlAttribute()
    {
        return $this->getFirstMediaUrl('profile') ?: asset('storage/images/users/default.png');
    }

    public function getIdImageAttribute()
    {
        return $this->getFirstMediaUrl('id_image') ?: asset('storage/images/users/default.png');
    }

    public function getLicenseImageAttribute()
    {
        return $this->getFirstMediaUrl('license_image') ?: asset('storage/images/users/default.png');
    }

    public function setPasswordAttribute($value)
    {
        if ($value) {
            $this->attributes['password'] = bcrypt($value);
        }
    }

    public function replays()
    {
        return $this->morphMany(ComplaintReplay::class, 'replayer');
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'notifiable')->orderBy('created_at', 'desc');
    }

    public function transactions()
    {
        return $this->morphMany(Transaction::class, 'transactionable')->latest();
    }

    public function settlements()
    {
        return $this->morphMany(Settlement::class, 'transactionable')->latest();
    }

    public function markAsActive()
    {
        $this->update(['code' => null, 'code_expire' => null, 'status' => 'active']);
        return $this;
    }

    public function sendVerificationCode()
    {
        $this->update([
            'code'        => $this->activationCode(),
            'code_expire' => Carbon::now()->addMinute(),
        ]);

        $this->sendCodeAtSms($this->code);
        // $this->sendEmail($this->code);

        return ['user' => new UserResource($this->refresh())];
    }

    private function activationCode()
    {
        return 12345;
        return mt_rand(1111, 9999);
    }

    public function sendCodeAtSms($code, $full_phone = null){
        $msg = trans('apis.activeCode');
        dispatch(new SendSms($full_phone ?? $this->full_phone, $msg . $code));
    }

    public function sendEmail($code, $full_phone = null){
        $msg = __('apis.activeCode');
        $data = ['title' => __('admin.reset_password'), 'message' => $msg.$code];
        dispatch(new SendEmailJob($this->email, $data));
    }

    public function logout()
    {
        $this->tokens()->delete();
        if (request()->device_id) {
            $this->devices()->where(['device_id' => request()->device_id])->delete();
        }
        return true;
    }

    public function devices()
    {
        return $this->morphMany(Device::class, 'morph');
    }

    public function login()
    {
        $this->updateUserDevice();
        $this->updateUserLang();
        $token = $this->createToken(request()->device_type)->plainTextToken;
        return UserResource::make($this)->setToken($token);
    }

    public function updateUserLang()
    {
        if (request()->header('Lang') != null
            && in_array(request()->header('Lang'), languages())) {
            $this->update(['lang' => request()->header('Lang')]);
        }
    }

    public function updateUserDevice()
    {
        if (request()->device_id) {
            $this->devices()->updateOrCreate([
                'device_id' => request()->device_id,
            ], [
                'device_type' => request()->device_type,
                'device_token' => request()->device_token,
            ]);
        }
    }

    public function city()
    {
        return $this->belongsTo(City::class, 'city_id', 'id');
    }

    public function region()
    {
        return $this->belongsTo(Region::class, 'region_id', 'id');
    }

    public function cart()
    {
        return $this->hasOne(Cart::class);
    }

    public function orderReports()
    {
        return $this->hasMany(OrderReport::class);
    }

    public function gifts()
    {
        return $this->belongsToMany(Gift::class, 'gift_users')->withTimestamps();
    }

    public function provider()
    {
        return $this->hasOne(Provider::class );
    }

    public function userUpdates()
    {
        return $this->hasMany(UserUpdate::class);
    }

    public function phoneUpdates()
    {
        return $this->hasMany(UserUpdate::class)->where('type', 'phone');
    }

    /**
     * Get loyalty points balance in SAR
     */
    public function getLoyaltyPointsValueAttribute()
    {
        // Assuming 1 point = 1 SAR (adjust as needed)
        return $this->loyalty_points ?? 0;
    }

    /**
     * Use loyalty points
     */
    public function useLoyaltyPoints($points)
    {
        if ($this->loyalty_points >= $points) {
            $this->decrement('loyalty_points', $points);
            return true;
        }
        return false;
    }

    /**
     * Add loyalty points
     */
    public function addLoyaltyPoints($points)
    {
        $this->increment('loyalty_points', $points);
        return $this;
    }

    /**
     * Calculate loyalty points earned from amount
     */
    public function calculateLoyaltyPointsEarned($amount)
    {
        // Assuming 1% of amount spent = points earned (adjust as needed)
        return floor($amount * 0.01);
    }
}
