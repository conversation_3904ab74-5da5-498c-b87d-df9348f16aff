<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class CartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'type' => $this->type,
            'provider_id' => $this->provider_id,

            // Provider details (for service carts)
            'provider' => $this->whenLoaded('provider', function() {
                return [
                    'id' => $this->provider->id,
                    'commercial_name' => $this->provider->commercial_name,
                    'status' => $this->provider->status,
                    'accept_orders' => $this->provider->accept_orders,
                ];
            }),

            // Cart items
            'items' => CartItemResource::collection($this->whenLoaded('items')),
            'total_items' => $this->total_items,

            // Cost breakdown
            'services_subtotal' => $this->services_subtotal,
            'products_subtotal' => $this->products_subtotal,
            'subtotal' => $this->subtotal,

            // Discounts
            'coupon_code' => $this->coupon_code,
            'discount_amount' => $this->discount_amount,
            'discount_percentage' => $this->coupon ? $this->coupon->discount : null,

            // Fees
            'booking_fee' => $this->booking_fee,
            'delivery_fee' => $this->delivery_fee,

            // Loyalty points
            'loyalty_points_used' => $this->loyalty_points_used,
            'user_loyalty_points_balance' => $this->user ? $this->user->loyalty_points : 0,
            'user_loyalty_points_value' => $this->user ? $this->user->loyalty_points_value : 0,

            // Totals
            'total' => $this->total,
            'final_total' => $this->total,
            'loyalty_points_earned' => $this->user ? $this->user->calculateLoyaltyPointsEarned($this->total) : 0,

            // Coupon details
            'coupon' => $this->whenLoaded('coupon', function() {
                return [
                    'coupon_num' => $this->coupon->coupon_num,
                    'type' => $this->coupon->type,
                    'discount' => $this->coupon->discount,
                    'max_discount' => $this->coupon->max_discount,
                    'status' => 'valid',
                ];
            }),
        ];
    }
}
