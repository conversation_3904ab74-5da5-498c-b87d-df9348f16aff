<div class="position-relative">
    
    
    
    
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>#</th>
                <th><?php echo e(__('admin.name')); ?></th>
                <th><?php echo e(__('admin.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_role">
                    <td><?php echo e($loop->iteration); ?></td>
                    <td><?php echo e($role->name); ?></td>
                    <td class="product-action">
                        <span class="action-edit text-primary"><a href="<?php echo e(route('admin.roles.edit', ['id' => $role->id])); ?>" class="btn btn-primary btn-sm"><i class="feather icon-edit"></i><?php echo e(__('admin.edit')); ?></a></span>
                        <?php if(auth()->guard('admin')->user()->role->id != $role->id): ?>
                        <span class="delete-row btn btn-danger btn-sm" data-url="<?php echo e(url('admin/roles/' . $role->id)); ?>"><i class="feather icon-trash"></i><?php echo e(__('admin.delete')); ?></span>
                        <?php endif; ?>

                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    
    <?php if($roles->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>

<?php if($roles->count() > 0 && $roles instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($roles->links()); ?>

    </div>
<?php endif; ?>
<?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/roles/table.blade.php ENDPATH**/ ?>